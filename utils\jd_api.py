#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东联盟API工具模块
包含京东联盟API的调用、签名生成等功能
"""

import hashlib
import time
import json
import aiohttp
from astrbot.api import logger

class JingdongAPI:
    """京东联盟API类"""
    
    def __init__(self, app_key, secret_key, union_id, union_id2, position_id):
        self.app_key = app_key
        self.secret_key = secret_key
        self.union_id = union_id
        self.union_id2 = union_id2
        self.position_id = position_id
        self.api_url = "https://api.jd.com/routerjson"
    
    def generate_sign(self, method, param_json, timestamp):
        """
        生成京东API签名
        """
        sorted_params = sorted([
            f"app_key{self.app_key}",
            f"formatjson",
            f"method{method}",
            f"param_json{param_json}",
            f"sign_methodmd5",
            f"timestamp{timestamp}",
            f"v1.0"
        ])
        param_string = ''.join(sorted_params)
        sign_string = f"{self.secret_key}{param_string}{self.secret_key}"
        md5 = hashlib.md5()
        md5.update(sign_string.encode('utf-8'))
        return md5.hexdigest().upper()
    
    async def call_api(self, material_id, is_union_link, session):
        """
        调用京东联盟API
        """
        timestamp = str(int(time.time() * 1000))
        method = "jd.union.open.promotion.byunionid.get"
        
        # 根据链接类型选择不同的联盟ID
        current_union_id = self.union_id2 if is_union_link else self.union_id
        
        param_data = {
            "promotionCodeReq": {
                "materialId": material_id,
                "unionId": int(current_union_id),
                "positionId": int(self.position_id)
            }
        }
        param_json = json.dumps(param_data, separators=(',', ':'))
        
        # 生成签名
        sign = self.generate_sign(method, param_json, timestamp)
        
        # 构建请求数据
        data = {
            "method": method,
            "app_key": self.app_key,
            "timestamp": timestamp,
            "format": "json",
            "v": "1.0",
            "sign_method": "md5",
            "sign": sign,
            "param_json": param_json
        }
        
        try:
            async with session.post(self.api_url, data=data) as response:
                if response.status == 200:
                    response_text = await response.text()
                    logger.info(f"京东 API 响应: {response_text}")
                    
                    response_data = json.loads(response_text)
                    jd_response = response_data.get('jd_union_open_promotion_byunionid_get_response', {})
                    result = json.loads(jd_response.get('result', '{}'))
                    
                    if 'code' in result and result['code'] != 200:
                        logger.error(f"京东 API 返回错误: {result.get('message', '未知错误')}")
                        return f"[链接生成失败: {result.get('message', '未知错误')}]"
                    
                    data_info = result.get('data', {})
                    if 'clickURL' in data_info:
                        click_url = data_info['clickURL']
                        logger.info(f"京东链接转换成功: {material_id} -> {click_url}")
                        return click_url
                    else:
                        logger.error("京东 API 响应中没有找到 clickURL")
                        return f"[链接生成失败: 响应格式错误]"
                else:
                    logger.error(f"京东 API 调用失败，状态码: {response.status}")
                    return f"[链接生成失败: HTTP {response.status}]"
        except json.JSONDecodeError as e:
            logger.error(f"解析京东 API 响应时出错: {e}")
            return f"[链接生成失败: 响应解析错误]"
        except Exception as e:
            logger.error(f"调用京东 API 时出错: {e}")
            return f"[链接生成失败: {str(e)}]"
