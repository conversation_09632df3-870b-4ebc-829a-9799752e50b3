#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块化代码测试脚本
验证各个模块是否能正常导入和工作
"""

import sys
import asyncio
import aiohttp

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from utils.config_manager import ConfigManager
        print("✓ ConfigManager 导入成功")
    except ImportError as e:
        print(f"✗ ConfigManager 导入失败: {e}")
        return False
    
    try:
        from utils.jd_api import JingdongAPI
        print("✓ JingdongAPI 导入成功")
    except ImportError as e:
        print(f"✗ JingdongAPI 导入失败: {e}")
        return False
    
    try:
        from utils.taobao_api import TaobaoAPI
        print("✓ TaobaoAPI 导入成功")
    except ImportError as e:
        print(f"✗ TaobaoAPI 导入失败: {e}")
        return False
    
    try:
        from utils.link_processor import LinkProcessor
        print("✓ LinkProcessor 导入成功")
    except ImportError as e:
        print(f"✗ LinkProcessor 导入失败: {e}")
        return False
    
    try:
        from utils.message_processor import MessageProcessor
        print("✓ MessageProcessor 导入成功")
    except ImportError as e:
        print(f"✗ MessageProcessor 导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n=== 测试配置管理器 ===")
    
    try:
        from utils.config_manager import ConfigManager
        
        # 创建配置管理器实例
        config_manager = ConfigManager()
        print("✓ ConfigManager 实例创建成功")
        
        # 测试默认配置
        jd_config = config_manager.get_jd_config()
        print(f"✓ 京东配置获取成功: {len(jd_config)} 项")
        
        taobao_config = config_manager.get_taobao_config()
        print(f"✓ 淘宝配置获取成功: {len(taobao_config)} 项")
        
        # 测试配置验证
        jd_valid = config_manager.validate_jd_config()
        print(f"✓ 京东配置验证: {'有效' if jd_valid else '无效'}")
        
        taobao_valid = config_manager.validate_taobao_config()
        print(f"✓ 淘宝配置验证: {'有效' if taobao_valid else '无效'}")
        
        # 测试状态获取
        status = config_manager.get_config_status()
        print(f"✓ 状态获取成功: {len(status)} 项")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_api_classes():
    """测试API类"""
    print("\n=== 测试API类 ===")
    
    try:
        from utils.jd_api import JingdongAPI
        from utils.taobao_api import TaobaoAPI
        
        # 测试京东API类
        jd_api = JingdongAPI("test_key", "test_secret", "test_union", "test_union2", "test_position")
        print("✓ JingdongAPI 实例创建成功")
        
        # 测试签名生成
        sign = jd_api.generate_sign("test_method", "{}", "123456")
        print(f"✓ 京东API签名生成成功: {len(sign)} 字符")
        
        # 测试淘宝API类
        taobao_api = TaobaoAPI("test_key", "test_secret", "test_adzone")
        print("✓ TaobaoAPI 实例创建成功")
        
        # 测试签名生成
        params = {"test": "value"}
        sign = taobao_api.generate_sign(params)
        print(f"✓ 淘宝API签名生成成功: {len(sign)} 字符")
        
        # 测试商品ID提取
        test_url = "https://item.taobao.com/item.htm?id=123456789"
        item_id = taobao_api.extract_item_id_from_url(test_url)
        print(f"✓ 商品ID提取成功: {item_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ API类测试失败: {e}")
        return False

def test_link_processor():
    """测试链接处理器"""
    print("\n=== 测试链接处理器 ===")
    
    try:
        from utils.link_processor import LinkProcessor
        
        # 创建链接处理器实例
        processor = LinkProcessor("https://test.api.url")
        print("✓ LinkProcessor 实例创建成功")
        
        # 测试链接检测
        test_text = "这是一个测试 https://item.jd.com/123.html 和 ￥ABC123￥"
        has_links = processor.has_links_to_process(test_text)
        print(f"✓ 链接检测: {'发现链接' if has_links else '无链接'}")
        
        # 测试链接提取
        links = processor.extract_links_by_type(test_text)
        print(f"✓ 链接提取成功: {sum(len(v) for v in links.values())} 个链接")
        
        # 测试京东商品ID提取
        jd_url = "https://item.jd.com/123456.html"
        material_id = processor.extract_jd_material_id(jd_url)
        print(f"✓ 京东商品ID提取: {material_id}")
        
        # 测试联盟链接判断
        is_union = processor.is_union_link("https://u.jd.com/test")
        print(f"✓ 联盟链接判断: {'是' if is_union else '否'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 链接处理器测试失败: {e}")
        return False

async def test_message_processor():
    """测试消息处理器"""
    print("\n=== 测试消息处理器 ===")
    
    try:
        from utils.config_manager import ConfigManager
        from utils.message_processor import MessageProcessor
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建模拟会话
        session = aiohttp.ClientSession()
        
        try:
            # 创建消息处理器
            processor = MessageProcessor(config_manager, session, session)
            print("✓ MessageProcessor 实例创建成功")
            
            # 测试消息分割
            long_text = "测试" * 1000
            chunks = processor.split_message(long_text, 100)
            print(f"✓ 消息分割测试: {len(chunks)} 个片段")
            
            # 测试帮助文本
            help_text = processor.get_help_text()
            print(f"✓ 帮助文本生成: {len(help_text)} 字符")
            
            # 测试状态文本
            status_text = processor.get_status_text()
            print(f"✓ 状态文本生成: {len(status_text)} 字符")
            
            return True
            
        finally:
            await session.close()
        
    except Exception as e:
        print(f"✗ 消息处理器测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("模块化代码测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("API类", test_api_classes),
        ("链接处理器", test_link_processor),
        ("消息处理器", test_message_processor)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！模块化代码工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")

if __name__ == "__main__":
    asyncio.run(main())
