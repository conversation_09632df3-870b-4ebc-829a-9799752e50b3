#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的模块导入测试
验证模块化代码的基本结构是否正确
"""

import sys
import os

def test_basic_imports():
    """测试基本模块导入"""
    print("=== 测试基本模块导入 ===")
    
    # 测试utils包导入
    try:
        import utils
        print("✓ utils 包导入成功")
    except ImportError as e:
        print(f"✗ utils 包导入失败: {e}")
        return False
    
    # 测试各个子模块导入
    modules = [
        ("config_manager", "ConfigManager"),
        ("jd_api", "JingdongAPI"),
        ("taobao_api", "TaobaoAPI"),
        ("link_processor", "LinkProcessor"),
        ("message_processor", "MessageProcessor")
    ]
    
    success_count = 0
    for module_name, class_name in modules:
        try:
            module = __import__(f"utils.{module_name}", fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✓ {module_name}.{class_name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module_name}.{class_name} 导入失败: {e}")
        except AttributeError as e:
            print(f"✗ {module_name}.{class_name} 类不存在: {e}")
    
    print(f"\n导入测试结果: {success_count}/{len(modules)} 个模块成功")
    return success_count == len(modules)

def test_class_instantiation():
    """测试类实例化（不需要外部依赖）"""
    print("\n=== 测试类实例化 ===")
    
    try:
        # 测试ConfigManager
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("✓ ConfigManager 实例化成功")
        
        # 测试基本方法
        jd_config = config_manager.get_jd_config()
        print(f"✓ 获取京东配置: {len(jd_config)} 项")
        
        taobao_config = config_manager.get_taobao_config()
        print(f"✓ 获取淘宝配置: {len(taobao_config)} 项")
        
    except Exception as e:
        print(f"✗ ConfigManager 测试失败: {e}")
        return False
    
    try:
        # 测试JingdongAPI
        from utils.jd_api import JingdongAPI
        jd_api = JingdongAPI("test", "test", "test", "test", "test")
        print("✓ JingdongAPI 实例化成功")
        
        # 测试签名生成
        sign = jd_api.generate_sign("test", "{}", "123")
        print(f"✓ 京东签名生成: {len(sign)} 字符")
        
    except Exception as e:
        print(f"✗ JingdongAPI 测试失败: {e}")
        return False
    
    try:
        # 测试TaobaoAPI
        from utils.taobao_api import TaobaoAPI
        taobao_api = TaobaoAPI("test", "test", "test")
        print("✓ TaobaoAPI 实例化成功")
        
        # 测试商品ID提取
        url = "https://item.taobao.com/item.htm?id=123456789"
        item_id = taobao_api.extract_item_id_from_url(url)
        print(f"✓ 商品ID提取: {item_id}")
        
    except Exception as e:
        print(f"✗ TaobaoAPI 测试失败: {e}")
        return False
    
    try:
        # 测试LinkProcessor
        from utils.link_processor import LinkProcessor
        processor = LinkProcessor("https://test.com")
        print("✓ LinkProcessor 实例化成功")
        
        # 测试链接检测
        text = "测试 https://item.jd.com/123.html 链接"
        has_links = processor.has_links_to_process(text)
        print(f"✓ 链接检测: {'有链接' if has_links else '无链接'}")
        
    except Exception as e:
        print(f"✗ LinkProcessor 测试失败: {e}")
        return False
    
    return True

def test_regex_patterns():
    """测试正则表达式模式"""
    print("\n=== 测试正则表达式模式 ===")
    
    try:
        from utils.link_processor import (
            JD_LINK_PATTERN, PRODEV_PATTERN, KOULING_PATTERN,
            TAOBAO_LINK_PATTERN, TAOBAO_TPWD_PATTERN
        )
        
        import re
        
        # 测试京东链接
        jd_urls = [
            "https://item.jd.com/123456.html",
            "https://u.jd.com/abc123",
            "https://jingfen.jd.com/test"
        ]
        
        for url in jd_urls:
            match = re.search(JD_LINK_PATTERN, url)
            print(f"京东链接 {url}: {'✓' if match else '✗'}")
        
        # 测试淘宝链接
        taobao_urls = [
            "https://item.taobao.com/item.htm?id=123456",
            "https://detail.tmall.com/item.htm?id=789012"
        ]
        
        for url in taobao_urls:
            match = re.search(TAOBAO_LINK_PATTERN, url)
            print(f"淘宝链接 {url}: {'✓' if match else '✗'}")
        
        # 测试口令
        kouling_tests = [
            "1:/￥ABC123￥",
            "￥XYZ789￥"
        ]
        
        for kouling in kouling_tests:
            if kouling.startswith("1:/"):
                match = re.search(KOULING_PATTERN, kouling)
                print(f"京东口令 {kouling}: {'✓' if match else '✗'}")
            else:
                match = re.search(TAOBAO_TPWD_PATTERN, kouling)
                print(f"淘口令 {kouling}: {'✓' if match else '✗'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 正则表达式测试失败: {e}")
        return False

def check_file_structure():
    """检查文件结构"""
    print("\n=== 检查文件结构 ===")
    
    required_files = [
        "utils/__init__.py",
        "utils/config_manager.py",
        "utils/jd_api.py",
        "utils/taobao_api.py",
        "utils/link_processor.py",
        "utils/message_processor.py",
        "main_modular.py",
        "_conf_schema.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺失文件: {len(missing_files)} 个")
        return False
    else:
        print(f"\n所有必需文件都存在")
        return True

def main():
    """主测试函数"""
    print("模块化代码基础测试")
    print("=" * 50)
    
    tests = [
        ("文件结构检查", check_file_structure),
        ("模块导入测试", test_basic_imports),
        ("类实例化测试", test_class_instantiation),
        ("正则表达式测试", test_regex_patterns)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 基础测试全部通过！代码结构正确。")
        print("\n下一步:")
        print("1. 安装依赖: pip install aiohttp beautifulsoup4")
        print("2. 配置API密钥")
        print("3. 运行完整测试: python test_taobao_convert.py")
    else:
        print("⚠️ 部分测试失败，请检查代码结构。")

if __name__ == "__main__":
    main()
