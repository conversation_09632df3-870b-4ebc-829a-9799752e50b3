#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包前清理脚本
清理Python缓存文件和临时文件，确保插件包干净
"""

import os
import shutil
import glob

def remove_pycache():
    """删除所有__pycache__目录"""
    print("清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    pycache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            pycache_dirs.append(pycache_path)
    
    # 删除找到的__pycache__目录
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"✓ 删除: {pycache_dir}")
        except Exception as e:
            print(f"✗ 删除失败: {pycache_dir} - {e}")
    
    if not pycache_dirs:
        print("✓ 没有找到__pycache__目录")

def remove_pyc_files():
    """删除所有.pyc文件"""
    print("\n清理.pyc文件...")
    
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"✓ 删除: {pyc_file}")
        except Exception as e:
            print(f"✗ 删除失败: {pyc_file} - {e}")
    
    if not pyc_files:
        print("✓ 没有找到.pyc文件")

def remove_temp_files():
    """删除临时文件"""
    print("\n清理临时文件...")
    
    temp_patterns = [
        '**/*.tmp',
        '**/*.temp',
        '**/*.log',
        '**/*~',
        '**/*.swp',
        '**/*.swo'
    ]
    
    temp_files = []
    for pattern in temp_patterns:
        temp_files.extend(glob.glob(pattern, recursive=True))
    
    for temp_file in temp_files:
        try:
            os.remove(temp_file)
            print(f"✓ 删除: {temp_file}")
        except Exception as e:
            print(f"✗ 删除失败: {temp_file} - {e}")
    
    if not temp_files:
        print("✓ 没有找到临时文件")

def remove_test_outputs():
    """删除测试输出文件"""
    print("\n清理测试输出...")
    
    test_files = [
        'taobao_config_sample.json',
        'test_output.txt',
        'debug.log'
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"✓ 删除: {test_file}")
            except Exception as e:
                print(f"✗ 删除失败: {test_file} - {e}")

def list_package_files():
    """列出将要打包的文件"""
    print("\n将要打包的文件:")
    
    # 核心文件
    core_files = [
        'main.py',
        '_conf_schema.json',
        'README.md',
        'metadata.yaml'
    ]
    
    # 可选文件
    optional_files = [
        'main_modular.py',
        'CODE_STRUCTURE.md',
        'DEPLOYMENT_GUIDE.md',
        'requirements.txt'
    ]
    
    # 工具目录
    utils_dir = 'utils'
    
    print("\n核心文件:")
    for file in core_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} (缺失)")
    
    print("\n可选文件:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"- {file} (不存在)")
    
    print(f"\n工具目录:")
    if os.path.exists(utils_dir):
        print(f"✓ {utils_dir}/")
        for root, dirs, files in os.walk(utils_dir):
            # 跳过__pycache__目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            for file in files:
                if not file.endswith('.pyc'):
                    rel_path = os.path.relpath(os.path.join(root, file))
                    print(f"  ✓ {rel_path}")
    else:
        print(f"- {utils_dir}/ (不存在)")

def main():
    """主函数"""
    print("AstrBot插件打包前清理工具")
    print("=" * 50)
    
    # 执行清理
    remove_pycache()
    remove_pyc_files()
    remove_temp_files()
    remove_test_outputs()
    
    # 列出文件
    list_package_files()
    
    print("\n" + "=" * 50)
    print("清理完成！现在可以安全地打包插件了。")
    print("\n建议的打包文件:")
    print("- main.py (必需)")
    print("- _conf_schema.json (必需)")
    print("- README.md (推荐)")
    print("- utils/ (如果使用模块化版本)")

if __name__ == "__main__":
    main()
