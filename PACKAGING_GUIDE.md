# 插件打包指南

## 🚨 解决AstrBot安装错误

您遇到的错误是因为压缩包中包含了Python缓存文件（`__pycache__`），现在已经清理完成。

### 错误原因
```
NotADirectoryError: [Errno 20] Not a directory: '/AstrBot/data/plugins/astrbot_jd/__pycache__/main.cpython-311.pyc'
```

这是因为AstrBot在解压插件时，将`.pyc`文件误认为是目录导致的。

## 📦 正确的打包方法

### 方案一：最小化打包（推荐）
只包含必需文件，适合大多数用户：

**必需文件：**
- `main.py` - 插件主文件
- `_conf_schema.json` - 配置模板
- `README.md` - 说明文档
- `metadata.yaml` - 插件元数据

### 方案二：完整打包
包含所有功能和文档：

**核心文件：**
- `main.py`
- `_conf_schema.json` 
- `README.md`
- `metadata.yaml`

**可选文件：**
- `utils/` - 工具模块（如果需要模块化版本）
- `CODE_STRUCTURE.md` - 代码结构说明
- `DEPLOYMENT_GUIDE.md` - 部署指南
- `requirements.txt` - 依赖列表

## 🧹 打包前清理

### 自动清理（推荐）
```bash
python clean_for_packaging.py
```

### 手动清理
删除以下文件/目录：
- `__pycache__/` 目录
- `*.pyc` 文件
- `*.tmp` 临时文件
- `*.log` 日志文件
- 测试输出文件

## 📋 打包步骤

### 1. 清理缓存文件
```bash
# 运行清理脚本
python clean_for_packaging.py

# 或手动删除（Windows）
Remove-Item -Recurse -Force __pycache__
Remove-Item -Recurse -Force utils\__pycache__

# 或手动删除（Linux/Mac）
find . -name "__pycache__" -type d -exec rm -rf {} +
find . -name "*.pyc" -delete
```

### 2. 选择文件
根据需要选择方案一或方案二的文件

### 3. 创建压缩包
```bash
# 方案一：最小化打包
zip -r astrbot_jd.zip main.py _conf_schema.json README.md metadata.yaml

# 方案二：完整打包
zip -r astrbot_jd.zip main.py _conf_schema.json README.md metadata.yaml utils/ CODE_STRUCTURE.md DEPLOYMENT_GUIDE.md requirements.txt
```

### 4. 验证压缩包
确保压缩包中没有：
- `__pycache__/` 目录
- `.pyc` 文件
- 临时文件

## 🔍 验证清单

打包前检查：
- [ ] 已运行清理脚本
- [ ] 没有`__pycache__`目录
- [ ] 没有`.pyc`文件
- [ ] `main.py`存在且完整
- [ ] `_conf_schema.json`存在且格式正确
- [ ] `README.md`包含使用说明

## 🚀 安装测试

### 1. 上传到AstrBot
- 进入AstrBot管理面板
- 选择"插件管理"
- 点击"上传插件"
- 选择清理后的压缩包

### 2. 验证安装
- 检查插件是否成功安装
- 查看配置选项是否正确显示
- 测试基本功能

## 🛠️ 故障排除

### 如果仍然出现错误：

1. **重新清理**
   ```bash
   python clean_for_packaging.py
   ```

2. **检查文件结构**
   ```bash
   # 查看压缩包内容
   unzip -l astrbot_jd.zip
   ```

3. **最小化测试**
   只打包`main.py`和`_conf_schema.json`进行测试

### 常见问题：

**Q: 压缩包太大？**
A: 只包含必需文件，删除测试文件和文档

**Q: 安装后配置不显示？**
A: 检查`_conf_schema.json`格式是否正确

**Q: 插件无法启动？**
A: 检查`main.py`中的导入语句和语法

## 📝 最终建议

### 推荐的最小打包内容：
```
astrbot_jd.zip
├── main.py
├── _conf_schema.json
├── README.md
└── metadata.yaml
```

这个配置包含了插件运行的所有必需文件，体积小，兼容性好。

### 如果需要模块化版本：
将`main_modular.py`重命名为`main.py`，并包含`utils/`目录。

---

现在您的插件包已经清理完成，可以安全地重新打包和安装了！
