# 京东线报处理插件使用指南

## 快速开始

### 1. 安装插件
将所有插件文件复制到 AstrBot 的插件目录中，然后重启 AstrBot 或在管理面板中重载插件。

### 2. 基本配置
在 AstrBot 管理面板中找到"京东线报处理器"插件，进行基本配置：

**必需配置**：
- 京东联盟 API 密钥（app_key, secret_key, union_id 等）

**可选配置**：
- 口令解析 API 地址
- 群组监控设置

### 3. 基本使用
插件安装后会自动工作，无需额外操作。当检测到支持的链接时，会自动处理并回复。

## 指令使用

### `/jd status`
查看插件当前状态，包括：
- 处理功能是否启用
- 群组监控状态
- 监控和转发群组数量

### `/jd enable` / `/jd disable`
启用或禁用链接处理功能

### `/jd help`
显示详细的帮助信息

## 群组监控配置

### 配置步骤
1. 在管理面板中找到插件配置
2. 展开"群组监控配置"
3. 设置以下参数：

```json
{
  "group_monitor": {
    "enable": true,
    "monitor_groups": ["群组ID1", "群组ID2"],
    "forward_groups": ["目标群组ID1", "目标群组ID2"],
    "forward_to_current": false
  }
}
```

### 获取群组ID
- QQ群：群号即为群组ID
- 其他平台：查看 AstrBot 日志或使用相关工具获取

### 配置说明
- `enable`: 是否启用群组监控
- `monitor_groups`: 要监控的群组ID列表
- `forward_groups`: 转发目标群组ID列表  
- `forward_to_current`: 是否同时在当前群组回复

## 使用场景

### 场景1：线报群监控
**需求**：监控线报群，自动转发处理后的链接到自己的群
**配置**：
```json
{
  "group_monitor": {
    "enable": true,
    "monitor_groups": ["线报群ID"],
    "forward_groups": ["自己的群ID"],
    "forward_to_current": false
  }
}
```

### 场景2：多群同步
**需求**：一个群的线报同步到多个群
**配置**：
```json
{
  "group_monitor": {
    "enable": true,
    "monitor_groups": ["源群ID"],
    "forward_groups": ["目标群1", "目标群2", "目标群3"],
    "forward_to_current": true
  }
}
```

### 场景3：普通使用
**需求**：在当前群直接处理链接
**配置**：
```json
{
  "group_monitor": {
    "enable": false
  }
}
```

## 支持的链接类型

### 京东商品链接
- `https://item.jd.com/12345678.html`
- `https://item.m.jd.com/product/12345678.html`
- `https://u.jd.com/abc123`
- `https://jingfen.jd.com/...`

### Prodev活动链接
- `https://prodev.m.jd.com/mall/active/xxx/index.html`
- `https://pro.m.jd.com/mall/active/xxx/index.html`

### 京东口令
- 格式：`数字:/￥字母数字￥`
- 示例：`1:/￥ABC123DEF￥`

### 短链接
- `https://3.cn/abc123`

## 青龙面板配置详细说明

### 青龙面板配置
插件通过青龙面板API获取JD_COOKIE变量，需要先配置青龙面板连接信息：

#### 1. 创建青龙面板应用
1. 登录青龙面板
2. 进入 **系统设置** -> **应用管理**
3. 点击 **创建应用**
4. 填写应用名称和权限
5. 获取 **Client ID** 和 **Client Secret**

#### 2. 配置插件
在AstrBot管理面板中配置青龙面板信息：
```json
{
  "qinglong_config": {
    "api_url": "http://localhost:5700",
    "client_id": "你的Client ID",
    "client_secret": "你的Client Secret"
  }
}
```

#### 3. 青龙面板环境变量
在青龙面板的环境变量中配置JD_COOKIE：
- 变量名: `JD_COOKIE`
- 变量值: `pt_key=1;pt_pin1;&pt_key=2;pt_pin2;&pt_key=3;pt_pin3;`
- 状态: 启用

### Cookie格式说明
- **新格式**: `pt_key=值;用户名;&pt_key=值;用户名;`
- **传统格式**: `pt_pin=用户名;其他字段;&pt_pin=用户名;其他字段;`
- 多个cookie用 `&` 分割
- 每个cookie必须包含用户标识信息

### API请求流程
1. 检测到京东API链接
2. 通过青龙面板API获取访问令牌
3. 从青龙面板获取JD_COOKIE环境变量
4. 解析cookie并为每个账号发送独立的API请求
5. 返回每个账号的请求结果
6. 自动处理常见的错误码

### 请求结果格式
```
京东API请求结果 (活动ID: xxx):
账号1(pt_pin1): {"code":"0","data":{"success":true}}
账号2(pt_pin2): {"code":"0","data":{"success":true}}
账号3(pt_pin3): {"code":"403","msg":"活动已结束"}
```

### Cookie格式示例解析

#### 你的格式示例
在青龙面板环境变量中配置：
```
变量名: JD_COOKIE
变量值: pt_key=1;pt_pin1;&pt_key=2;pt_pin2;&pt_key=3;pt_pin3;
状态: 启用
```

**解析结果**:
- Cookie 1: `pt_key=1;pt_pin1` → 用户: `pt_pin1`
- Cookie 2: `pt_key=2;pt_pin2` → 用户: `pt_pin2`
- Cookie 3: `pt_key=3;pt_pin3` → 用户: `pt_pin3`

**API请求时会显示**:
```
京东API请求结果 (活动ID: xxx):
账号1(pt_pin1): {"code":"0","data":{"success":true}}
账号2(pt_pin2): {"code":"0","data":{"success":true}}
账号3(pt_pin3): {"code":"0","data":{"success":true}}
```

### 配置选项
```json
{
  "qinglong_config": {
    "api_url": "http://localhost:5700",
    "client_id": "你的Client ID",
    "client_secret": "你的Client Secret"
  },
  "jd_api_config": {
    "enable_api_request": true,
    "request_timeout": 10
  }
}
```

## 常见问题

### Q: 插件不工作怎么办？
A: 
1. 检查插件是否启用：`/jd status`
2. 检查配置是否正确
3. 查看 AstrBot 日志是否有错误信息

### Q: 群组监控不生效？
A:
1. 确认群组ID配置正确
2. 确认机器人在目标群组中
3. 检查群组权限设置

### Q: 转发失败怎么办？
A:
1. 检查目标群组ID是否正确
2. 确认机器人在目标群组中有发言权限
3. 查看日志中的错误信息

### Q: 如何获取群组ID？
A:
1. QQ群：直接使用群号
2. 其他平台：查看 AstrBot 日志或使用 `/jd status` 在群中查看当前群组信息

### Q: 京东API请求失败怎么办？
A:
1. 检查cookie文件是否存在和格式是否正确
2. 确认cookie是否有效（未过期）
3. 检查API请求功能是否启用：`/jd status`
4. 查看日志中的详细错误信息

### Q: 青龙面板连接失败？
A:
1. 检查青龙面板地址是否正确
2. 确认Client ID和Client Secret是否有效
3. 检查网络连接，确保能访问青龙面板
4. 查看青龙面板应用管理中的权限设置

### Q: Cookie读取失败？
A:
1. 检查青龙面板中JD_COOKIE变量是否存在
2. 确认JD_COOKIE变量状态为启用
3. 检查JD_COOKIE变量格式：`pt_key=1;pt_pin1;&pt_key=2;pt_pin2;`
4. 确保cookie中包含用户标识信息

### Q: 为什么有些账号请求失败？
A:
1. 检查对应账号的cookie是否有效
2. 某些活动可能有参与限制
3. 账号可能触发了风控机制
4. 活动可能已经结束或暂停

## 注意事项

1. **API配置**：需要有效的京东联盟账号和API密钥
2. **网络连接**：部分功能需要稳定的网络连接
3. **权限设置**：确保机器人在目标群组中有发言权限
4. **频率限制**：避免过于频繁的API调用
5. **隐私保护**：注意保护API密钥等敏感信息
6. **Cookie安全**：妥善保管京东账号cookie，避免泄露
7. **青龙面板安全**：保护好Client ID和Client Secret
8. **多账号管理**：定期检查各账号cookie的有效性
9. **活动限制**：某些京东活动可能有地域或账号限制
10. **API权限**：确保青龙面板应用有足够的权限访问环境变量

## 技术支持

如遇到问题，请：
1. 查看 AstrBot 日志
2. 检查插件配置
3. 联系插件作者或在相关社区求助
