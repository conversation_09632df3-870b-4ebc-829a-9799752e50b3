#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝联盟配置检查脚本
用于验证配置是否正确
"""

import json
import os
import sys

def check_config_file():
    """检查配置文件"""
    print("=== 检查配置文件 ===")
    
    # 检查配置文件是否存在
    config_files = ["_conf_schema.json", "config.json"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 找到配置文件: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                if "taobao_api" in config:
                    print("✓ 找到淘宝API配置项")
                    check_taobao_config(config["taobao_api"])
                else:
                    print("✗ 未找到淘宝API配置项")
                    
            except json.JSONDecodeError as e:
                print(f"✗ 配置文件JSON格式错误: {e}")
            except Exception as e:
                print(f"✗ 读取配置文件失败: {e}")
        else:
            print(f"- 配置文件不存在: {config_file}")

def check_taobao_config(taobao_config):
    """检查淘宝API配置"""
    print("\n=== 检查淘宝API配置 ===")
    
    required_fields = {
        "app_key": "淘宝联盟APP KEY",
        "app_secret": "淘宝联盟APP SECRET", 
        "adzone_id": "推广位ID"
    }
    
    optional_fields = {
        "sub_pid": "三方分成PID",
        "enable_taobao_convert": "启用淘宝转链",
        "enable_taobao_tpwd": "启用淘口令处理"
    }
    
    # 检查必需字段
    print("必需配置项:")
    all_required_ok = True
    for field, desc in required_fields.items():
        value = taobao_config.get(field, "")
        if value and value.strip():
            print(f"✓ {desc}: 已配置")
        else:
            print(f"✗ {desc}: 未配置或为空")
            all_required_ok = False
    
    # 检查可选字段
    print("\n可选配置项:")
    for field, desc in optional_fields.items():
        value = taobao_config.get(field)
        if field.startswith("enable_"):
            status = "启用" if value else "禁用"
            print(f"- {desc}: {status}")
        else:
            status = "已配置" if value and str(value).strip() else "未配置"
            print(f"- {desc}: {status}")
    
    return all_required_ok

def check_schema_file():
    """检查配置模板文件"""
    print("\n=== 检查配置模板 ===")
    
    if os.path.exists("_conf_schema.json"):
        try:
            with open("_conf_schema.json", 'r', encoding='utf-8') as f:
                schema = json.load(f)
                
            if "taobao_api" in schema:
                print("✓ 配置模板包含淘宝API配置")
                taobao_schema = schema["taobao_api"]
                
                if "items" in taobao_schema:
                    items = taobao_schema["items"]
                    required_items = ["app_key", "app_secret", "adzone_id", 
                                    "enable_taobao_convert", "enable_taobao_tpwd"]
                    
                    print("配置项检查:")
                    for item in required_items:
                        if item in items:
                            print(f"✓ {item}")
                        else:
                            print(f"✗ {item}")
                else:
                    print("✗ 配置模板格式错误")
            else:
                print("✗ 配置模板缺少淘宝API配置")
                
        except Exception as e:
            print(f"✗ 读取配置模板失败: {e}")
    else:
        print("✗ 配置模板文件不存在")

def generate_sample_config():
    """生成示例配置"""
    print("\n=== 生成示例配置 ===")
    
    sample_config = {
        "taobao_api": {
            "app_key": "你的淘宝联盟APP_KEY",
            "app_secret": "你的淘宝联盟APP_SECRET",
            "adzone_id": "你的推广位ID",
            "sub_pid": "mm_xxx_xxx_xxx（可选）",
            "enable_taobao_convert": True,
            "enable_taobao_tpwd": True
        }
    }
    
    try:
        with open("taobao_config_sample.json", 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)
        print("✓ 已生成示例配置文件: taobao_config_sample.json")
        print("请参考此文件配置您的淘宝联盟API")
    except Exception as e:
        print(f"✗ 生成示例配置失败: {e}")

def print_help():
    """打印帮助信息"""
    print("\n=== 配置帮助 ===")
    print("1. 获取淘宝联盟API密钥:")
    print("   - 访问: https://aff-open.taobao.com/developer/")
    print("   - 创建应用并获取APP KEY和APP SECRET")
    print("   - 在推广位管理中创建推广位获取ID")
    print()
    print("2. 在AstrBot管理面板中配置:")
    print("   - 进入插件配置页面")
    print("   - 填入获取的API密钥和推广位ID")
    print("   - 启用相应的功能开关")
    print()
    print("3. 测试配置:")
    print("   - 运行: python test_taobao_convert.py")
    print("   - 在聊天中发送淘宝链接测试")

def main():
    """主函数"""
    print("淘宝联盟配置检查工具")
    print("=" * 50)
    
    check_schema_file()
    check_config_file()
    generate_sample_config()
    print_help()
    
    print("\n检查完成！")

if __name__ == "__main__":
    main()
