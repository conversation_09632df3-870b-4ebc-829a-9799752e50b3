# 🔧 淘宝联盟API配置指南

## 🚨 解决您遇到的问题

根据您的日志，有两个问题需要解决：

### 1. 淘口令转换失败
```
[Plug] [ERRO] [astrbot_jd.main:300]: 淘口令转换失败: ￥fspjVqQdUFg￥
```

**原因**: 淘宝联盟API未配置或配置不正确

### 2. 淘宝推广链接未处理
```
https://s.click.taobao.com/iFUeGNr
```

**原因**: 已修复，现在支持 `s.click.taobao.com` 链接

## ⚙️ 配置步骤

### 1. 申请淘宝联盟API

1. 访问 [淘宝联盟开放平台](https://open.taobao.com/)
2. 注册并创建应用
3. 获取以下信息：
   - `App Key`
   - `App Secret`
   - `推广位ID (adzone_id)`

### 2. 在AstrBot中配置

在AstrBot管理面板的插件配置中添加：

```json
{
  "xianbao": {
    "enable_processing": true,
    "taobao_api": {
      "app_key": "你的淘宝联盟App_Key",
      "app_secret": "你的淘宝联盟App_Secret",
      "adzone_id": "你的推广位ID",
      "enable_taobao_convert": true,
      "enable_taobao_tpwd": true,
      "taobao_format_style": "detailed"
    }
  }
}
```

### 3. 配置说明

| 参数 | 必需 | 说明 | 示例 |
|------|------|------|------|
| `app_key` | ✅ | 淘宝联盟应用Key | "12345678" |
| `app_secret` | ✅ | 淘宝联盟应用Secret | "abcdef123456" |
| `adzone_id` | ✅ | 推广位ID | "123456789" |
| `sub_pid` | ❌ | 三方分成PID（可选） | "mm_123_456_789" |
| `enable_taobao_convert` | ✅ | 启用淘宝转链 | true |
| `enable_taobao_tpwd` | ✅ | 启用淘口令处理 | true |
| `taobao_format_style` | ❌ | 显示格式 | "detailed" |

## 🎯 测试配置

### 1. 测试淘宝商品链接
```
https://item.taobao.com/item.htm?id=123456789
```

### 2. 测试淘口令
```
￥ABC123￥
```

### 3. 测试推广链接
```
https://s.click.taobao.com/iFUeGNr
```

## 📋 预期效果

### 配置正确后的输出

#### 淘口令处理（detailed格式）
**输入**: `￥fspjVqQdUFg￥`

**输出**:
```
达利园八宝粥360g*12罐
https://s.click.taobao.com/converted_link
￥fspjVqQdUFg￥
```

#### 推广链接处理
**输入**: `https://s.click.taobao.com/iFUeGNr`

**输出**:
```
淘宝推广商品
https://s.click.taobao.com/iFUeGNr
```

## 🔍 故障排除

### 1. 淘口令转换失败

**可能原因**:
- API密钥未配置
- API密钥错误
- 推广位ID错误
- 网络连接问题

**解决方法**:
```bash
# 检查配置
/xianbao status

# 查看日志
# 确认API调用是否成功
```

### 2. 链接不处理

**可能原因**:
- 功能未启用
- 链接格式不支持

**解决方法**:
```json
{
  "taobao_api": {
    "enable_taobao_convert": true,
    "enable_taobao_tpwd": true
  }
}
```

### 3. 格式不符合预期

**解决方法**:
```json
{
  "taobao_api": {
    "taobao_format_style": "detailed"  // 或 "simple"
  }
}
```

## 🚀 完整配置示例

### 最小配置（仅启用基本功能）
```json
{
  "xianbao": {
    "enable_processing": true,
    "taobao_api": {
      "app_key": "你的App_Key",
      "app_secret": "你的App_Secret",
      "adzone_id": "你的推广位ID",
      "enable_taobao_convert": true,
      "enable_taobao_tpwd": true
    }
  }
}
```

### 完整配置（包含所有选项）
```json
{
  "xianbao": {
    "enable_processing": true,
    "jd_api": {
      "app_key": "京东App_Key",
      "secret_key": "京东Secret_Key",
      "union_id": "京东联盟ID",
      "position_id": "京东推广位ID"
    },
    "taobao_api": {
      "app_key": "淘宝App_Key",
      "app_secret": "淘宝App_Secret",
      "adzone_id": "淘宝推广位ID",
      "sub_pid": "三方分成PID（可选）",
      "enable_taobao_convert": true,
      "enable_taobao_tpwd": true,
      "taobao_format_style": "detailed"
    }
  }
}
```

## 📝 注意事项

1. **API申请**: 淘宝联盟API需要单独申请，有一定门槛
2. **推广位**: 需要在淘宝联盟后台创建推广位
3. **权限**: 确保API有转链权限
4. **配额**: 注意API调用次数限制
5. **测试**: 建议先用测试商品验证配置

## 🎉 配置完成后

1. 重启AstrBot插件
2. 使用 `/xianbao status` 检查状态
3. 发送测试链接验证功能
4. 观察日志确认API调用成功

现在您的淘宝链接和淘口令应该能正常处理了！
