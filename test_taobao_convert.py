#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝联盟转链功能测试脚本
用于验证淘宝转链功能是否正常工作
"""

import asyncio
import aiohttp
import re
from main import (
    extract_item_id_from_url,
    convert_taobao_link,
    convert_taobao_tpwd,
    create_taobao_tpwd,
    TAOBAO_API_CONFIG,
    TAOBAO_LINK_PATTERN,
    TAOBAO_SHORT_LINK_PATTERN,
    TAOBAO_TPWD_PATTERN
)

# 测试用例
TEST_CASES = {
    "淘宝商品链接": [
        "https://item.taobao.com/item.htm?id=123456789",
        "https://detail.tmall.com/item.htm?id=987654321",
        "https://chaoshi.detail.tmall.com/item.htm?id=555666777"
    ],
    "淘宝短链接": [
        "https://m.tb.cn/h.ABC123",
        "https://tb.cn/ABC123"
    ],
    "淘口令": [
        "￥ABC123￥",
        "￥XYZ789￥"
    ]
}

async def test_regex_patterns():
    """测试正则表达式匹配"""
    print("=== 测试正则表达式匹配 ===")
    
    # 测试淘宝链接正则
    for link in TEST_CASES["淘宝商品链接"]:
        match = re.search(TAOBAO_LINK_PATTERN, link)
        print(f"淘宝链接 {link}: {'✓' if match else '✗'}")
    
    # 测试短链接正则
    for link in TEST_CASES["淘宝短链接"]:
        match = re.search(TAOBAO_SHORT_LINK_PATTERN, link)
        print(f"短链接 {link}: {'✓' if match else '✗'}")
    
    # 测试淘口令正则
    for tpwd in TEST_CASES["淘口令"]:
        match = re.search(TAOBAO_TPWD_PATTERN, tpwd)
        print(f"淘口令 {tpwd}: {'✓' if match else '✗'}")

def test_extract_item_id():
    """测试商品ID提取"""
    print("\n=== 测试商品ID提取 ===")
    
    test_urls = [
        "https://item.taobao.com/item.htm?id=123456789",
        "https://detail.tmall.com/item.htm?id=987654321&spm=xxx",
        "https://item.taobao.com/item.htm?spm=xxx&id=555666777&other=param"
    ]
    
    for url in test_urls:
        item_id = extract_item_id_from_url(url)
        print(f"URL: {url}")
        print(f"商品ID: {item_id}")
        print()

async def test_api_config():
    """测试API配置"""
    print("=== 测试API配置 ===")
    
    required_fields = ["app_key", "app_secret", "adzone_id"]
    config_status = {}
    
    for field in required_fields:
        value = TAOBAO_API_CONFIG.get(field)
        config_status[field] = "✓" if value else "✗"
        print(f"{field}: {config_status[field]} ({'已配置' if value else '未配置'})")
    
    # 检查功能开关
    convert_enabled = TAOBAO_API_CONFIG.get("enable_taobao_convert", False)
    tpwd_enabled = TAOBAO_API_CONFIG.get("enable_taobao_tpwd", False)
    
    print(f"淘宝转链开关: {'✓' if convert_enabled else '✗'}")
    print(f"淘口令处理开关: {'✓' if tpwd_enabled else '✗'}")
    
    return all(config_status.values()) and convert_enabled

async def test_link_conversion():
    """测试链接转换功能"""
    print("\n=== 测试链接转换功能 ===")
    
    if not await test_api_config():
        print("⚠️ API配置不完整，跳过实际转换测试")
        return
    
    # 测试商品链接转换
    test_url = "https://item.taobao.com/item.htm?id=123456789"
    print(f"测试链接: {test_url}")
    
    try:
        result = await convert_taobao_link(test_url)
        if result != test_url:
            print(f"✓ 转换成功: {result}")
        else:
            print("✗ 转换失败或未启用")
    except Exception as e:
        print(f"✗ 转换出错: {e}")

async def test_tpwd_conversion():
    """测试淘口令转换功能"""
    print("\n=== 测试淘口令转换功能 ===")
    
    if not TAOBAO_API_CONFIG.get("enable_taobao_tpwd"):
        print("⚠️ 淘口令处理未启用，跳过测试")
        return
    
    test_tpwd = "￥TEST123￥"
    print(f"测试淘口令: {test_tpwd}")
    
    try:
        result = await convert_taobao_tpwd(test_tpwd)
        if result != test_tpwd:
            print(f"✓ 转换成功: {result}")
        else:
            print("✗ 转换失败")
    except Exception as e:
        print(f"✗ 转换出错: {e}")

async def test_tpwd_creation():
    """测试淘口令生成功能"""
    print("\n=== 测试淘口令生成功能 ===")
    
    if not TAOBAO_API_CONFIG.get("enable_taobao_tpwd"):
        print("⚠️ 淘口令处理未启用，跳过测试")
        return
    
    test_url = "https://s.click.taobao.com/t?e=test"
    print(f"测试URL: {test_url}")
    
    try:
        result = await create_taobao_tpwd(test_url, "测试商品")
        if result != test_url:
            print(f"✓ 生成成功: {result}")
        else:
            print("✗ 生成失败")
    except Exception as e:
        print(f"✗ 生成出错: {e}")

def print_usage_guide():
    """打印使用指南"""
    print("\n=== 使用指南 ===")
    print("1. 确保已在AstrBot管理面板中配置淘宝联盟API")
    print("2. 启用相应的功能开关")
    print("3. 在聊天中发送包含淘宝链接或淘口令的消息")
    print("4. 观察转换结果和日志输出")
    print("\n支持的消息格式示例:")
    print("- 商品链接: https://item.taobao.com/item.htm?id=123456789")
    print("- 淘口令: ￥ABC123￥")
    print("- 短链接: https://m.tb.cn/h.ABC123")

async def main():
    """主测试函数"""
    print("淘宝联盟转链功能测试")
    print("=" * 50)
    
    # 运行所有测试
    await test_regex_patterns()
    test_extract_item_id()
    await test_link_conversion()
    await test_tpwd_conversion()
    await test_tpwd_creation()
    print_usage_guide()
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
