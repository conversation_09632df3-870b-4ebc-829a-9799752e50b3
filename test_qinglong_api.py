#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
青龙面板API测试脚本
用于测试青龙面板API连接和JD_COOKIE获取
"""

import asyncio
import aiohttp
import time
import json

# 测试配置 - 请根据实际情况修改
QL_API_URL = "http://localhost:5700"  # 青龙面板地址
QL_CLIENT_ID = ""  # 请填入你的Client ID
QL_CLIENT_SECRET = ""  # 请填入你的Client Secret

# 全局变量
QL_TOKEN = ""
QL_TOKEN_EXPIRES = 0

async def get_ql_token():
    """
    获取青龙面板访问令牌
    基于青龙面板OpenAPI标准实现
    """
    global QL_TOKEN, QL_TOKEN_EXPIRES

    # 检查现有令牌是否有效
    if QL_TOKEN and time.time() < QL_TOKEN_EXPIRES - 300:  # 提前5分钟刷新
        print(f"✅ 使用现有令牌: {QL_TOKEN[:30]}...")
        return QL_TOKEN

    print("🔑 正在获取青龙面板访问令牌...")

    try:
        # 使用GET方式获取token，符合青龙面板OpenAPI标准
        url = f"{QL_API_URL}/open/auth/token?client_id={QL_CLIENT_ID}&client_secret={QL_CLIENT_SECRET}"

        print(f"📡 请求地址: {url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                print(f"📊 响应状态: {response.status}")
                response_text = await response.text()
                print(f"📄 响应内容: {response_text}")

                if response.status == 200:
                    result = await response.json()
                    if result.get("code") == 200:
                        token_data = result["data"]
                        # 构造完整的Authorization头
                        QL_TOKEN = f"{token_data['token_type']} {token_data['token']}"
                        QL_TOKEN_EXPIRES = time.time() + token_data["expiration"]
                        print(f"✅ 令牌获取成功: {QL_TOKEN[:30]}...")
                        print(f"⏰ 令牌过期时间: {time.ctime(QL_TOKEN_EXPIRES)}")
                        return QL_TOKEN
                    else:
                        print(f"❌ 青龙面板认证失败: {result.get('message', '未知错误')}")
                else:
                    print(f"❌ 青龙面板API请求失败: HTTP {response.status}")

    except Exception as e:
        print(f"❌ 获取青龙面板令牌时发生错误: {e}")

    return None

async def get_jd_cookies():
    """
    从青龙面板API获取JD_COOKIE变量
    """
    print("\n正在从青龙面板获取JD_COOKIE变量...")

    # 检查配置
    if not QL_CLIENT_ID or not QL_CLIENT_SECRET:
        print("❌ 青龙面板配置不完整，请配置Client ID和Client Secret")
        return []

    # 获取访问令牌
    token = await get_ql_token()
    if not token:
        print("❌ 无法获取青龙面板访问令牌")
        return []

    try:
        # 请求环境变量，使用标准的青龙面板OpenAPI格式
        headers = {
            "Authorization": token,  # token已经包含了Bearer前缀
            "Content-Type": "application/json"
        }

        # 使用searchValue参数来优化查询，只获取JD_COOKIE相关的变量
        print(f"📡 请求地址: {QL_API_URL}/open/envs?searchValue=JD_COOKIE")

        async with aiohttp.ClientSession() as session:
            async with session.get(f"{QL_API_URL}/open/envs?searchValue=JD_COOKIE", headers=headers) as response:
                print(f"📊 响应状态: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    if result.get("code") == 200:
                        envs = result.get("data", [])
                        print(f"📋 获取到 {len(envs)} 个环境变量")

                        # 查找JD_COOKIE变量
                        jd_cookie_env = None
                        for env in envs:
                            if env.get("name") == "JD_COOKIE":
                                jd_cookie_env = env
                                print(f"✅ 找到JD_COOKIE变量:")
                                print(f"   ID: {env.get('id')}")
                                print(f"   名称: {env.get('name')}")
                                print(f"   状态: {env.get('status')} (0=启用, 1=禁用)")
                                print(f"   值长度: {len(env.get('value', ''))} 字符")
                                print(f"   值预览: {env.get('value', '')[:100]}...")
                                break

                        if jd_cookie_env and jd_cookie_env.get("status") == 0:
                            jd_cookie_value = jd_cookie_env.get("value", "")
                            return parse_cookies(jd_cookie_value)
                        elif jd_cookie_env:
                            print("⚠️  JD_COOKIE变量存在但已禁用，请在青龙面板中启用")
                        else:
                            print("❌ 未找到JD_COOKIE变量，请在青龙面板环境变量中创建")
                            print("   变量名: JD_COOKIE")
                            print("   状态: 启用")
                    else:
                        print(f"❌ 青龙面板API返回错误: {result.get('message', '未知错误')}")
                else:
                    print(f"❌ 青龙面板环境变量API请求失败: HTTP {response.status}")
                    response_text = await response.text()
                    print(f"   响应内容: {response_text[:200]}...")

    except Exception as e:
        print(f"❌ 从青龙面板读取JD_COOKIE时发生错误: {e}")

    return []

def parse_cookies(cookie_string):
    """
    解析cookie字符串
    """
    print(f"\n正在解析cookie字符串...")
    print(f"原始字符串长度: {len(cookie_string)} 字符")
    print(f"原始字符串预览: {cookie_string[:200]}...")
    
    # 按&分割cookie
    cookies = [cookie.strip() for cookie in cookie_string.split('&') if cookie.strip()]
    print(f"分割后得到 {len(cookies)} 个cookie")
    
    for i, cookie in enumerate(cookies, 1):
        print(f"Cookie {i}: {cookie[:50]}...")
        
        # 尝试提取pt_pin
        pt_pin = extract_pt_pin(cookie)
        print(f"  用户标识: {pt_pin}")
    
    return cookies

def extract_pt_pin(cookie):
    """
    从cookie中提取pt_pin
    """
    import re
    
    # 首先尝试标准格式 pt_pin=value
    match = re.search(r'pt_pin=([^;]+)', cookie)
    if match:
        return match.group(1)
    
    # 尝试新格式，查找pt_key后面的pt_pin值
    parts = cookie.split(';')
    pt_key_found = False
    
    for part in parts:
        part = part.strip()
        if part.startswith('pt_key='):
            pt_key_found = True
            continue
        
        # 如果找到了pt_key，下一个非空部分就是pt_pin
        if pt_key_found and part and not part.startswith('pt_'):
            return part
        
        # 如果是pt_pin开头但不是pt_pin=格式，直接返回
        if pt_key_found and part.startswith('pt_pin') and '=' not in part:
            return part
    
    return "未知"

async def test_qinglong_api():
    """
    测试青龙面板API功能
    """
    print("青龙面板API测试")
    print("=" * 50)
    
    # 检查配置
    if not QL_CLIENT_ID or not QL_CLIENT_SECRET:
        print("错误: 请先配置QL_CLIENT_ID和QL_CLIENT_SECRET")
        print("请在青龙面板的应用管理中创建应用并获取Client ID和Secret")
        return
    
    print(f"青龙面板地址: {QL_API_URL}")
    print(f"Client ID: {QL_CLIENT_ID}")
    print(f"Client Secret: {QL_CLIENT_SECRET[:10]}...")
    
    # 测试获取令牌
    print("\n1. 测试获取访问令牌...")
    token = await get_ql_token()
    
    if not token:
        print("令牌获取失败，无法继续测试")
        return
    
    # 测试获取JD_COOKIE
    print("\n2. 测试获取JD_COOKIE...")
    cookies = await get_jd_cookies()
    
    if cookies:
        print(f"\n✅ 成功获取到 {len(cookies)} 个cookie")
        print("\n🎉 青龙面板API配置正确，插件可以正常获取JD_COOKIE")
    else:
        print("\n❌ 未获取到有效的cookie")
        print("\n请检查:")
        print("1. 青龙面板中是否存在名为 JD_COOKIE 的环境变量")
        print("2. JD_COOKIE 变量状态是否为启用")
        print("3. JD_COOKIE 变量值是否正确配置")

if __name__ == "__main__":
    # 提示用户配置
    if not QL_CLIENT_ID or not QL_CLIENT_SECRET:
        print("请先配置青龙面板信息:")
        print("1. 在青龙面板中进入 系统设置 -> 应用管理")
        print("2. 创建新应用，获取Client ID和Client Secret")
        print("3. 修改本脚本中的QL_CLIENT_ID和QL_CLIENT_SECRET变量")
        print("4. 确保青龙面板地址QL_API_URL正确")
    
    asyncio.run(test_qinglong_api())
