# 🔧 淘宝联盟抓包API使用指南

## 🎯 解决您的问题

根据您提供的抓包数据，我已经实现了使用抓包API的方法来处理淘宝转链。这样可以避免复杂的签名计算问题。

## 📋 抓包数据分析

您提供的抓包数据：
```
http://gw.api.taobao.com/router/rest
adzone_id=109936550232&biz_scene_id=1&material_list=%ef%bf%a5wG48VqRALrr%ef%bf%a5&site_id=1231650351&method=taobao.tbk.sc.general.link.convert&v=2.0&sign_method=hmac&app_key=25232084&format=xml&partner_id=top-sdk-net-20241117&timestamp=2025-06-08+11%3a59%3a33&session=700001003354d0f3b9b74cd97207884afa2b77ace419cf613e11625eb9372031d8249cb2263333475&sign=CD7590D75E9374CCE0E29431421C3AD3
```

## ⚙️ 配置步骤

### 1. 获取Session值

您需要从浏览器中获取session值：

1. 打开淘宝联盟后台
2. 打开浏览器开发者工具（F12）
3. 进行一次转链操作
4. 在Network标签中找到API请求
5. 复制session参数的值

### 2. 在AstrBot中配置

```json
{
  "xianbao": {
    "enable_processing": true,
    "taobao_api": {
      "app_key": "25232084",
      "adzone_id": "109936550232",
      "site_id": "1231650351",
      "session": "您的session值",
      "enable_taobao_convert": true,
      "enable_taobao_tpwd": true,
      "use_captured_api": true,
      "taobao_format_style": "detailed"
    }
  }
}
```

### 3. 配置说明

| 参数 | 值 | 说明 |
|------|-----|------|
| `app_key` | `25232084` | 从抓包获取的固定值 |
| `adzone_id` | `109936550232` | 您的推广位ID |
| `site_id` | `1231650351` | 从抓包获取的站点ID |
| `session` | `您的session值` | **需要您提供** |
| `use_captured_api` | `true` | 启用抓包API方法 |

## 🔑 获取Session的详细步骤

### 方法一：浏览器开发者工具

1. **登录淘宝联盟**
   - 访问 https://pub.alimama.com/
   - 登录您的账号

2. **打开开发者工具**
   - 按F12或右键选择"检查"
   - 切换到"Network"标签

3. **执行转链操作**
   - 在联盟后台进行一次链接转换
   - 或者访问转链工具页面

4. **查找API请求**
   - 在Network中找到 `gw.api.taobao.com` 的请求
   - 点击查看请求详情

5. **复制Session**
   - 在请求参数中找到 `session=` 
   - 复制等号后面的长字符串

### 方法二：从Cookie中提取

Session值通常也存在于Cookie中，格式类似：
```
session=700001003354d0f3b9b74cd97207884afa2b77ace419cf613e11625eb9372031d8249cb2263333475
```

## 🎯 测试配置

### 1. 配置示例

```json
{
  "xianbao": {
    "taobao_api": {
      "session": "700001003354d0f3b9b74cd97207884afa2b77ace419cf613e11625eb9372031d8249cb2263333475",
      "enable_taobao_tpwd": true,
      "use_captured_api": true
    }
  }
}
```

### 2. 测试淘口令

发送消息：
```
￥fspjVqQdUFg￥
```

### 3. 预期输出

**detailed格式**：
```
达利园八宝粥360g*12罐
https://s.click.taobao.com/converted_link
￥fspjVqQdUFg￥
```

**simple格式**：
```
https://s.click.taobao.com/converted_link
```

## 🔧 工作原理

### 1. API调用流程

```
淘口令 → 抓包API → XML响应 → 解析链接 → 格式化输出
```

### 2. 请求参数

```
POST http://gw.api.taobao.com/router/rest
{
  "adzone_id": "109936550232",
  "biz_scene_id": "1", 
  "material_list": "￥淘口令￥",
  "site_id": "1231650351",
  "method": "taobao.tbk.sc.general.link.convert",
  "session": "您的session值",
  "app_key": "25232084",
  "format": "xml"
}
```

### 3. 响应解析

API返回XML格式，插件会解析：
- `<click_url>` - 转换后的推广链接
- `<title>` - 商品标题

## ⚠️ 注意事项

### 1. Session有效期

- Session会过期，需要定期更新
- 如果转换失败，检查session是否过期

### 2. 备用方案

如果抓包API失败，插件会自动尝试：
1. 官方API方法（需要app_secret）
2. 返回原始链接

### 3. 日志监控

关注AstrBot日志：
```
[INFO] 使用抓包API处理淘口令: ￥...￥
[INFO] 淘口令转换成功(抓包API): ￥...￥ -> https://...
```

## 🚀 优势

### 相比官方API

1. **无需签名计算** - 避免复杂的HMAC签名
2. **配置简单** - 只需要session值
3. **实时有效** - 使用真实的浏览器session

### 相比手动转换

1. **自动化处理** - 无需手动操作
2. **批量转换** - 支持多个链接同时处理
3. **格式化输出** - 统一的显示格式

## 🔍 故障排除

### 1. Session过期

**症状**: 转换失败，返回原链接
**解决**: 重新获取session值并更新配置

### 2. 推广位无效

**症状**: API调用成功但无转换结果
**解决**: 检查adzone_id是否正确

### 3. 网络问题

**症状**: API调用超时
**解决**: 检查网络连接和防火墙设置

## 📝 配置模板

### 完整配置

```json
{
  "xianbao": {
    "enable_processing": true,
    "jd_api": {
      "app_key": "京东APP_KEY",
      "secret_key": "京东SECRET_KEY",
      "union_id": "京东联盟ID",
      "position_id": "京东推广位ID"
    },
    "taobao_api": {
      "app_key": "25232084",
      "adzone_id": "109936550232", 
      "site_id": "1231650351",
      "session": "您的session值",
      "enable_taobao_convert": true,
      "enable_taobao_tpwd": true,
      "use_captured_api": true,
      "taobao_format_style": "detailed"
    }
  }
}
```

### 最小配置（仅淘宝抓包API）

```json
{
  "xianbao": {
    "taobao_api": {
      "session": "您的session值",
      "enable_taobao_tpwd": true,
      "use_captured_api": true
    }
  }
}
```

现在您只需要提供session值，就可以使用抓包的API方法来处理淘宝转链了！
