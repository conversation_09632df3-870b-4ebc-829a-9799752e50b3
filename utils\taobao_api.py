#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝联盟API工具模块
包含淘宝联盟API的调用、签名生成等功能
"""

import hashlib
import time
import json
import re
import aiohttp
from astrbot.api import logger

class TaobaoAPI:
    """淘宝联盟API类"""
    
    def __init__(self, app_key, app_secret, adzone_id, sub_pid=None):
        self.app_key = app_key
        self.app_secret = app_secret
        self.adzone_id = adzone_id
        self.sub_pid = sub_pid
        self.api_url = "https://eco.taobao.com/router/rest"
    
    def generate_sign(self, params):
        """
        生成淘宝API签名
        """
        # 排序参数
        sorted_params = sorted(params.items())
        # 拼接参数字符串
        param_string = ''.join([f"{k}{v}" for k, v in sorted_params])
        # 生成签名字符串
        sign_string = f"{self.app_secret}{param_string}{self.app_secret}"
        # 计算MD5
        md5 = hashlib.md5()
        md5.update(sign_string.encode('utf-8'))
        return md5.hexdigest().upper()
    
    async def call_api(self, method, params, session):
        """
        调用淘宝联盟API
        """
        if not self.app_key or not self.app_secret:
            logger.error("淘宝联盟API配置不完整")
            return None
        
        # 基础参数
        base_params = {
            "method": method,
            "app_key": self.app_key,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "format": "json",
            "v": "2.0",
            "sign_method": "md5"
        }
        
        # 合并参数
        all_params = {**base_params, **params}
        
        # 生成签名
        sign = self.generate_sign(all_params)
        all_params["sign"] = sign
        
        try:
            # 调用API
            async with session.post(self.api_url, data=all_params) as response:
                if response.status == 200:
                    response_text = await response.text()
                    logger.debug(f"淘宝API响应: {response_text}")
                    return json.loads(response_text)
                else:
                    logger.error(f"淘宝API调用失败，状态码: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"调用淘宝API时出错: {e}")
            return None
    
    def extract_item_id_from_url(self, url):
        """
        从淘宝链接中提取商品ID
        """
        # 匹配 item.taobao.com 的商品ID
        match = re.search(r'item\.taobao\.com/item\.htm\?.*?id=(\d+)', url)
        if match:
            return match.group(1)
        
        # 匹配 detail.tmall.com 的商品ID
        match = re.search(r'detail\.tmall\.com/item\.htm\?.*?id=(\d+)', url)
        if match:
            return match.group(1)
        
        # 匹配其他格式
        match = re.search(r'[?&]id=(\d+)', url)
        if match:
            return match.group(1)
        
        return None
    
    async def convert_item_link(self, url, session):
        """
        转换淘宝商品链接为推广链接
        """
        # 提取商品ID
        item_id = self.extract_item_id_from_url(url)
        if not item_id:
            logger.error(f"无法从链接中提取商品ID: {url}")
            return url
        
        # 调用淘宝联盟API转链
        params = {
            "num_iids": item_id,
            "adzone_id": self.adzone_id,
            "fields": "num_iid,click_url"
        }
        
        response = await self.call_api("taobao.tbk.item.convert", params, session)
        if response and "tbk_item_convert_response" in response:
            results = response["tbk_item_convert_response"].get("results", {})
            items = results.get("n_tbk_item", [])
            if items and len(items) > 0:
                click_url = items[0].get("click_url")
                if click_url:
                    logger.info(f"淘宝链接转换成功: {url} -> {click_url}")
                    return click_url
        
        logger.error(f"淘宝链接转换失败: {url}")
        return url
    
    async def convert_tpwd(self, tpwd, session):
        """
        解析淘口令并转换为推广链接
        """
        # 调用淘口令解析API
        params = {
            "password_content": tpwd,
            "adzone_id": self.adzone_id
        }
        
        # 如果有三方分成PID，使用三方分成转链API
        if self.sub_pid:
            params["sub_pid"] = self.sub_pid
            response = await self.call_api("taobao.tbk.tpwd.share.convert", params, session)
            if response and "tbk_tpwd_share_convert_response" in response:
                data = response["tbk_tpwd_share_convert_response"].get("data", {})
                click_url = data.get("click_url")
                if click_url:
                    logger.info(f"淘口令转换成功(三方): {tpwd} -> {click_url}")
                    return click_url
        else:
            # 使用普通转链API
            response = await self.call_api("taobao.tbk.tpwd.convert", params, session)
            if response and "tbk_tpwd_convert_response" in response:
                data = response["tbk_tpwd_convert_response"].get("data", {})
                click_url = data.get("click_url")
                if click_url:
                    logger.info(f"淘口令转换成功: {tpwd} -> {click_url}")
                    return click_url
        
        logger.error(f"淘口令转换失败: {tpwd}")
        return tpwd
    
    async def create_tpwd(self, url, text="", session=None):
        """
        生成淘口令
        """
        params = {
            "url": url,
            "text": text or "好物推荐"
        }
        
        response = await self.call_api("taobao.tbk.tpwd.create", params, session)
        if response and "tbk_tpwd_create_response" in response:
            data = response["tbk_tpwd_create_response"].get("data", {})
            model = data.get("model")
            if model:
                logger.info(f"淘口令生成成功: {url} -> {model}")
                return model
        
        logger.error(f"淘口令生成失败: {url}")
        return url
