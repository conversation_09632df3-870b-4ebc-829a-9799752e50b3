# 淘宝联盟API配置指南

## 1. 获取淘宝联盟API密钥

### 步骤1：注册淘宝联盟账号
1. 访问 https://aff-open.taobao.com/developer/index.htm
2. 使用淘宝账号登录
3. 完成实名认证和联盟账号绑定

### 步骤2：创建应用
1. 进入"应用管理" -> "创建应用"
2. 填写应用信息（应用名称、应用描述等）
3. 选择应用类型（建议选择"网站应用"）
4. 提交审核，等待通过

### 步骤3：获取API密钥
1. 应用审核通过后，进入应用详情
2. 获取以下信息：
   - **App Key**: 应用的唯一标识
   - **App Secret**: 应用密钥
   - **推广位ID**: 在"推广位管理"中创建并获取

## 2. AstrBot配置示例

在AstrBot管理面板中，按以下格式配置：

```json
{
  "taobao_api": {
    "app_key": "你的App Key",
    "app_secret": "你的App Secret", 
    "adzone_id": "你的推广位ID",
    "sub_pid": "mm_xxx_xxx_xxx",  // 可选，三方分成PID
    "enable_taobao_convert": true,  // 启用淘宝链接转换
    "enable_taobao_tpwd": true      // 启用淘口令处理
  }
}
```

## 3. 推广位ID获取方法

1. 登录淘宝联盟后台
2. 进入"推广管理" -> "推广位管理"
3. 创建新的推广位或使用现有推广位
4. 复制推广位ID（格式通常为数字）

## 4. 三方分成PID（可选）

如果您需要使用三方分成功能：
1. 推广位ID格式：`mm_xxx_xxx_xxx`
2. 在配置中填入完整的三方分成PID
3. 系统会自动使用三方分成转链API

## 5. 测试配置

配置完成后，可以发送以下测试消息：

### 测试淘宝商品链接
```
https://item.taobao.com/item.htm?id=123456789
```

### 测试淘口令
```
￥ABC123￥
```

### 测试天猫链接
```
https://detail.tmall.com/item.htm?id=123456789
```

## 6. 常见问题

### Q: 转链失败怎么办？
A: 检查以下项目：
- App Key和App Secret是否正确
- 推广位ID是否有效
- 网络连接是否正常
- 商品是否支持推广

### Q: 如何查看转链日志？
A: 在AstrBot日志中查看，关键词：
- "淘宝链接转换成功"
- "淘口令转换成功"
- "淘宝API响应"

### Q: 支持哪些淘宝链接格式？
A: 支持以下格式：
- item.taobao.com
- detail.tmall.com
- chaoshi.detail.tmall.com
- detail.tmall.hk
- m.tb.cn (短链接)
- tb.cn (短链接)

## 7. API限制说明

- 淘宝联盟API有调用频率限制
- 建议合理使用，避免频繁调用
- 部分商品可能不支持转链
- 转链成功率取决于商品状态和推广政策
