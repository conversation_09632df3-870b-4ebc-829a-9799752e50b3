#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息处理器模块
处理包含各种链接的消息
"""

import re
import asyncio
from astrbot.api import logger
from .link_processor import LinkProcessor
from .jd_api import JingdongAPI
from .taobao_api import TaobaoAPI

class MessageProcessor:
    """消息处理器类"""
    
    def __init__(self, config_manager, session_default, session_no_ssl):
        self.config_manager = config_manager
        self.session_default = session_default
        self.session_no_ssl = session_no_ssl
        self.link_processor = LinkProcessor(config_manager.get_kouling_api_url())
        
        # 初始化API实例
        self._init_apis()
    
    def _init_apis(self):
        """初始化API实例"""
        # 京东API
        jd_config = self.config_manager.get_jd_config()
        self.jd_api = JingdongAPI(
            app_key=jd_config["app_key"],
            secret_key=jd_config["secret_key"],
            union_id=jd_config["union_id"],
            union_id2=jd_config["union_id2"],
            position_id=jd_config["position_id"]
        )
        
        # 淘宝API
        taobao_config = self.config_manager.get_taobao_config()
        self.taobao_api = TaobaoAPI(
            app_key=taobao_config["app_key"],
            app_secret=taobao_config["app_secret"],
            adzone_id=taobao_config["adzone_id"],
            sub_pid=taobao_config.get("sub_pid")
        )
    
    def split_message(self, text, max_length=4096):
        """
        分割长消息
        """
        if len(text) <= max_length:
            return [text]
        
        lines = text.split('\n')
        chunks = []
        current_chunk = ""
        
        for line in lines:
            if len(current_chunk) + len(line) + 1 <= max_length:
                if current_chunk:
                    current_chunk += '\n' + line
                else:
                    current_chunk = line
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = line
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    async def process_message_links(self, message_text):
        """
        处理消息中的链接
        """
        if not self.config_manager.is_processing_enabled():
            return message_text
        
        # 检查消息是否包含需要处理的链接
        if not self.link_processor.has_links_to_process(message_text):
            return message_text
        
        logger.info("检测到需要处理的链接，开始处理...")
        
        # 按行处理消息
        message_lines = message_text.split('\n')
        new_message_lines = []
        
        for line in message_lines:
            processed_line = await self._process_line(line)
            new_message_lines.append(processed_line)
        
        return '\n'.join(new_message_lines)
    
    async def _process_line(self, line):
        """
        处理单行文本中的链接
        """
        # 提取各种类型的链接
        links = self.link_processor.extract_links_by_type(line)
        
        # 处理京东商品链接
        for link in links['jd_links']:
            logger.info(f"提取到的京东链接: {link}")
            material_id = self.link_processor.extract_jd_material_id(link)
            if material_id:
                is_union = self.link_processor.is_union_link(link)
                result = await self.jd_api.call_api(material_id, is_union, self.session_default)
                if result and result.startswith("http"):
                    line = line.replace(link, result)
                else:
                    line = line.replace(link, f"[生成失败]({link})")
        
        # 处理Prodev链接
        for link in links['prodev_links']:
            logger.info(f"提取到的Prodev链接: {link}")
            result = await self.link_processor.process_prodev_link(link, self.session_no_ssl)
            line = line.replace(link, result)
        
        # 处理京东口令
        for kouling in links['kouling']:
            logger.info(f"提取到的口令: {kouling}")
            result = await self.link_processor.process_kouling(kouling, self.session_default)
            line = line.replace(kouling, result)
        
        # 处理淘宝商品链接
        if self.config_manager.is_taobao_convert_enabled():
            for link in links['taobao_links']:
                logger.info(f"提取到的淘宝链接: {link}")
                result = await self.taobao_api.convert_item_link(link, self.session_default)
                if result != link:
                    line = line.replace(link, result)
        
        # 处理淘宝短链接
        if self.config_manager.is_taobao_convert_enabled():
            for link in links['taobao_short_links']:
                logger.info(f"提取到的淘宝短链接: {link}")
                # 先解析短链接获取真实链接
                resolved_url = await self.link_processor.short_to_real(link, self.session_default)
                if resolved_url and resolved_url.startswith("http"):
                    # 再转换为推广链接
                    converted_url = await self.taobao_api.convert_item_link(resolved_url, self.session_default)
                    line = line.replace(link, converted_url)
                else:
                    line = line.replace(link, f"[解析失败]({link})")
        
        # 处理淘口令
        if self.config_manager.is_taobao_tpwd_enabled():
            for tpwd in links['taobao_tpwd']:
                logger.info(f"提取到的淘口令: {tpwd}")
                result = await self.taobao_api.convert_tpwd(tpwd, self.session_default)
                if result != tpwd:
                    line = line.replace(tpwd, result)
        
        # 处理3.cn短链接
        for short_url in links['short_urls']:
            logger.info(f"提取到的短链接: {short_url}")
            resolved_url = await self.link_processor.short_to_real(short_url, self.session_default)
            if resolved_url and resolved_url.startswith("http"):
                line = line.replace(short_url, resolved_url)
            else:
                line = line.replace(short_url, f"[解析失败]({short_url})")
        
        return line
    
    def get_help_text(self):
        """
        获取帮助文本
        """
        help_text = """线报处理插件使用说明:
/xianbao status - 查看当前状态
/xianbao enable - 启用处理功能
/xianbao disable - 禁用处理功能
/xianbao help - 显示帮助信息

支持的链接类型:
- 京东商品链接 (item.jd.com, u.jd.com等)
- Prodev活动链接 (prodev.m.jd.com, pro.m.jd.com)
- 京东口令 (格式: 数字:/￥...￥)
- 淘宝商品链接 (item.taobao.com, detail.tmall.com等)
- 淘宝短链接 (m.tb.cn, tb.cn)
- 淘口令 (格式: ￥...￥)
- 3.cn短链接

注意: 淘宝转链功能需要在配置中启用并设置相关API参数"""
        
        return help_text
    
    def get_status_text(self):
        """
        获取状态文本
        """
        status = self.config_manager.get_config_status()
        
        status_text = f"""当前状态:
处理功能: {'启用' if status['processing_enabled'] else '禁用'}
京东API: {'正常' if status['jd_config_valid'] else '配置不完整'}
淘宝转链: {'启用' if status['taobao_convert_enabled'] else '禁用'}
淘口令处理: {'启用' if status['taobao_tpwd_enabled'] else '禁用'}
淘宝API: {'正常' if status['taobao_config_valid'] else '配置不完整'}"""
        
        return status_text
