import aiohttp
import asyncio
import re
import hashlib
import time
import json
from urllib.parse import quote
from bs4 import BeautifulSoup
import ssl

# astrbot插件相关导入
from astrbot.api.event import filter, AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api import logger, AstrBotConfig

# 默认配置文件（如果用户没有配置则使用默认值）
DEFAULT_JD_API_CONFIG = {
  "app_key": "4363c9848d8a43c8b7b9e957d3adcbcf",
  "method": "jd.union.open.promotion.byunionid.get",
  "format_type": "json",
  "sign_method": "md5",
  "v": "1.0",
  "secret_key": "905878013e044d2ba3adeebb05338086",
  "union_id": "1000054340",
  "union_id2": "1002084276",
  "position_id": "3003477075"
}

# 淘宝联盟API默认配置
DEFAULT_TAOBAO_API_CONFIG = {
  "app_key": "25232084",  # 从抓包获取的app_key
  "app_secret": "",
  "adzone_id": "109936550232",  # 从抓包获取的adzone_id
  "site_id": "1231650351",  # 从抓包获取的site_id
  "session": "",  # 需要用户提供session
  "enable_taobao_convert": False,
  "enable_taobao_tpwd": False,
  "taobao_format_style": "detailed",  # detailed: 详细格式, simple: 简单格式
  "use_captured_api": True  # 使用抓包的API方法
}

# 全局配置变量
JD_API_CONFIG = DEFAULT_JD_API_CONFIG.copy()
TAOBAO_API_CONFIG = DEFAULT_TAOBAO_API_CONFIG.copy()
KOULING_API_URL = "https://jd.zack.xin/api/jd/ulink.php"
ENABLE_PROCESSING = True

# 正则表达式模式
JD_LINK_PATTERN = r'(https://(?:u|item|item\.m|jingfen)\.jd\.com/\S*)'
PRODEV_PATTERN = r'(https://(?:prodev\.m\.jd\.com|pro\.m\.jd\.com)/\S*)'
KOULING_PATTERN = r'(\d+:/[^\n￥]*￥[A-Za-z0-9]+￥)'  # 优化后的口令正则
SHORT_URL_PATTERN = r'(https?://3\.cn/\S+)'

# 淘宝链接正则表达式
TAOBAO_LINK_PATTERN = r'(https://(?:item\.taobao\.com|detail\.tmall\.com|chaoshi\.detail\.tmall\.com|detail\.tmall\.hk|s\.click\.taobao\.com)/[^\s]*)'
TAOBAO_SHORT_LINK_PATTERN = r'(https://(?:m\.tb\.cn|tb\.cn)/[^\s]*)'
TAOBAO_TPWD_PATTERN = r'(￥[A-Za-z0-9]+￥)'  # 淘口令正则

# 口令API配置
JD_KOULING_API = {
  "url": "https://jd.zack.xin/api/jd/ulink.php",
  "headers": {
      "Host": "jd.zack.xin",
      "Connection": "keep-alive",
      "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
      "Accept": "application/json, text/plain, */*",
      "sec-ch-ua-platform": '"Windows"',
      "sec-ch-ua-mobile": "?0",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Content-Type": "application/x-www-form-urlencoded",
      "Origin": "https://jd.zack.xin",
      "Sec-Fetch-Site": "same-origin",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Dest": "empty",
      "Referer": "https://jd.zack.xin/jd/zhushou/",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      "Accept-Encoding": "gzip, deflate",
  }
}

# 创建 SSL 上下文以禁用 SSL 检查
ssl_context_no_verify = ssl.create_default_context()
ssl_context_no_verify.check_hostname = False
ssl_context_no_verify.verify_mode = ssl.CERT_NONE

# 全局会话变量
session_default = None  # 默认会话，启用 SSL 验证
session_no_ssl = None    # 禁用 SSL 验证的会话

def generate_sign(app_key, method, param_json, timestamp, secret_key):
  """
  生成签名
  """
  sorted_params = sorted([
      f"app_key{app_key}",
      f"formatjson",
      f"method{method}",
      f"param_json{param_json}",
      f"sign_methodmd5",
      f"timestamp{timestamp}",
      f"v1.0"
  ])
  param_string = ''.join(sorted_params)
  sign_string = f"{secret_key}{param_string}{secret_key}"
  md5 = hashlib.md5()
  md5.update(sign_string.encode('utf-8'))
  return md5.hexdigest().upper()

def generate_taobao_sign(params, secret):
  """
  生成淘宝API签名
  """
  # 排序参数
  sorted_params = sorted(params.items())
  # 拼接参数字符串
  param_string = ''.join([f"{k}{v}" for k, v in sorted_params])
  # 生成签名字符串
  sign_string = f"{secret}{param_string}{secret}"
  # 计算MD5
  md5 = hashlib.md5()
  md5.update(sign_string.encode('utf-8'))
  return md5.hexdigest().upper()

def find_key_recursive(data, target_key):
  """
  递归搜索嵌套字典中的指定键，并返回所有匹配的值。

  Args:
      data (dict or list): 要搜索的字典或列表。
      target_key (str): 目标键名。

  Returns:
      list: 所有匹配键的值列表。
  """
  results = []
  if isinstance(data, dict):
      for key, value in data.items():
          if key == target_key:
              results.append(value)
          elif isinstance(value, (dict, list)):
              results.extend(find_key_recursive(value, target_key))
  elif isinstance(data, list):
      for item in data:
          if isinstance(item, (dict, list)):
              results.extend(find_key_recursive(item, target_key))
  return results

def extract_item_id_from_url(url):
  """
  从淘宝链接中提取商品ID
  """
  # 匹配 item.taobao.com 的商品ID
  match = re.search(r'item\.taobao\.com/item\.htm\?.*?id=(\d+)', url)
  if match:
      return match.group(1)

  # 匹配 detail.tmall.com 的商品ID
  match = re.search(r'detail\.tmall\.com/item\.htm\?.*?id=(\d+)', url)
  if match:
      return match.group(1)

  # 匹配其他格式
  match = re.search(r'[?&]id=(\d+)', url)
  if match:
      return match.group(1)

  return None

async def call_taobao_captured_api(material_list):
  """
  使用抓包的淘宝联盟API方法
  """
  if not TAOBAO_API_CONFIG.get("session"):
      logger.error("淘宝联盟session未配置")
      return None

  # 构建请求参数（基于抓包数据）
  params = {
      "adzone_id": TAOBAO_API_CONFIG.get("adzone_id", "109936550232"),
      "biz_scene_id": "1",
      "material_list": material_list,  # 淘口令或链接
      "site_id": TAOBAO_API_CONFIG.get("site_id", "1231650351"),
      "method": "taobao.tbk.sc.general.link.convert",
      "v": "2.0",
      "sign_method": "hmac",
      "app_key": TAOBAO_API_CONFIG.get("app_key", "25232084"),
      "format": "xml",
      "partner_id": "top-sdk-net-20241117",
      "timestamp": time.strftime("%Y-%m-%d %H:%M:%S").replace(" ", "+"),
      "session": TAOBAO_API_CONFIG["session"]
  }

  # 这里需要生成HMAC签名，但由于没有secret，我们先尝试不签名
  # 或者使用固定的sign（从抓包获取）
  params["sign"] = "CD7590D75E9374CCE0E29431421C3AD3"  # 从抓包获取的签名

  try:
      # 调用API
      async with session_default.post("http://gw.api.taobao.com/router/rest", data=params) as response:
          if response.status == 200:
              response_text = await response.text()
              logger.debug(f"淘宝抓包API响应: {response_text}")
              return response_text
          else:
              logger.error(f"淘宝抓包API调用失败，状态码: {response.status}")
              return None
  except Exception as e:
      logger.error(f"调用淘宝抓包API时出错: {e}")
      return None

async def call_taobao_api(method, params):
  """
  调用淘宝联盟API（保留原方法作为备用）
  """
  if not TAOBAO_API_CONFIG.get("app_key") or not TAOBAO_API_CONFIG.get("app_secret"):
      logger.error("淘宝联盟API配置不完整")
      return None

  # 基础参数
  base_params = {
      "method": method,
      "app_key": TAOBAO_API_CONFIG["app_key"],
      "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
      "format": "json",
      "v": "2.0",
      "sign_method": "md5"
  }

  # 合并参数
  all_params = {**base_params, **params}

  # 生成签名
  sign = generate_taobao_sign(all_params, TAOBAO_API_CONFIG["app_secret"])
  all_params["sign"] = sign

  try:
      # 调用API
      async with session_default.post("https://eco.taobao.com/router/rest", data=all_params) as response:
          if response.status == 200:
              response_text = await response.text()
              logger.debug(f"淘宝API响应: {response_text}")
              return json.loads(response_text)
          else:
              logger.error(f"淘宝API调用失败，状态码: {response.status}")
              return None
  except Exception as e:
      logger.error(f"调用淘宝API时出错: {e}")
      return None

async def convert_taobao_link(url):
  """
  转换淘宝商品链接为推广链接，返回格式化信息
  """
  if not TAOBAO_API_CONFIG.get("enable_taobao_convert"):
      return url  # 如果未启用转链，返回原链接

  # 检查是否已经是推广链接
  if "s.click.taobao.com" in url:
      logger.info(f"检测到淘宝推广链接，直接返回: {url}")
      format_style = TAOBAO_API_CONFIG.get("taobao_format_style", "detailed")
      if format_style == "simple":
          return url
      else:
          # 尝试获取商品标题（可选）
          return f"淘宝推广商品\n{url}"

  # 提取商品ID
  item_id = extract_item_id_from_url(url)
  if not item_id:
      logger.error(f"无法从链接中提取商品ID: {url}")
      return url

  # 调用淘宝联盟API转链
  params = {
      "num_iids": item_id,
      "adzone_id": TAOBAO_API_CONFIG["adzone_id"],
      "fields": "num_iid,click_url,title"
  }

  response = await call_taobao_api("taobao.tbk.item.convert", params)
  if response and "tbk_item_convert_response" in response:
      results = response["tbk_item_convert_response"].get("results", {})
      items = results.get("n_tbk_item", [])
      if items and len(items) > 0:
          item_data = items[0]
          click_url = item_data.get("click_url")
          title = item_data.get("title", "")
          if click_url:
              logger.info(f"淘宝链接转换成功: {url} -> {click_url}")
              # 根据配置返回不同格式
              format_style = TAOBAO_API_CONFIG.get("taobao_format_style", "detailed")
              if format_style == "simple":
                  return click_url
              else:  # detailed格式
                  if title:
                      return f"{title}\n{click_url}"
                  else:
                      return click_url

  logger.error(f"淘宝链接转换失败: {url}")
  return url

async def convert_taobao_tpwd(tpwd):
  """
  解析淘口令并转换为推广链接，返回格式化信息
  """
  if not TAOBAO_API_CONFIG.get("enable_taobao_tpwd"):
      return tpwd  # 如果未启用淘口令处理，返回原口令

  # 优先使用抓包的API方法
  if TAOBAO_API_CONFIG.get("use_captured_api", True) and TAOBAO_API_CONFIG.get("session"):
      logger.info(f"使用抓包API处理淘口令: {tpwd}")
      response = await call_taobao_captured_api(tpwd)
      if response:
          # 解析XML响应（抓包API返回XML格式）
          try:
              # 这里需要解析XML响应，提取转换后的链接
              # 由于是XML格式，我们需要简单的文本解析
              if "click_url" in response:
                  # 提取链接（简化处理）
                  import re
                  url_match = re.search(r'<click_url><!\[CDATA\[(.*?)\]\]></click_url>', response)
                  title_match = re.search(r'<title><!\[CDATA\[(.*?)\]\]></title>', response)

                  if url_match:
                      click_url = url_match.group(1)
                      title = title_match.group(1) if title_match else ""

                      logger.info(f"淘口令转换成功(抓包API): {tpwd} -> {click_url}")

                      # 根据配置返回不同格式
                      format_style = TAOBAO_API_CONFIG.get("taobao_format_style", "detailed")
                      if format_style == "simple":
                          return click_url
                      else:  # detailed格式
                          if title:
                              return f"{title}\n{click_url}\n{tpwd}"
                          else:
                              return f"{click_url}\n{tpwd}"
          except Exception as e:
              logger.error(f"解析抓包API响应失败: {e}")

  # 备用：使用官方API方法
  if TAOBAO_API_CONFIG.get("app_key") and TAOBAO_API_CONFIG.get("app_secret"):
      # 调用淘口令解析API
      params = {
          "password_content": tpwd,
          "adzone_id": TAOBAO_API_CONFIG["adzone_id"]
      }

      # 如果有三方分成PID，使用三方分成转链API
      if TAOBAO_API_CONFIG.get("sub_pid"):
          params["sub_pid"] = TAOBAO_API_CONFIG["sub_pid"]
          response = await call_taobao_api("taobao.tbk.tpwd.share.convert", params)
          if response and "tbk_tpwd_share_convert_response" in response:
              data = response["tbk_tpwd_share_convert_response"].get("data", {})
              click_url = data.get("click_url")
              title = data.get("title", "")
              if click_url:
                  logger.info(f"淘口令转换成功(三方): {tpwd} -> {click_url}")
                  # 根据配置返回不同格式
                  format_style = TAOBAO_API_CONFIG.get("taobao_format_style", "detailed")
                  if format_style == "simple":
                      return click_url
                  else:  # detailed格式
                      if title:
                          return f"{title}\n{click_url}\n{tpwd}"
                      else:
                          return f"{click_url}\n{tpwd}"
      else:
          # 使用普通转链API
          response = await call_taobao_api("taobao.tbk.tpwd.convert", params)
          if response and "tbk_tpwd_convert_response" in response:
              data = response["tbk_tpwd_convert_response"].get("data", {})
              click_url = data.get("click_url")
              title = data.get("title", "")
              if click_url:
                  logger.info(f"淘口令转换成功: {tpwd} -> {click_url}")
                  # 根据配置返回不同格式
                  format_style = TAOBAO_API_CONFIG.get("taobao_format_style", "detailed")
                  if format_style == "simple":
                      return click_url
                  else:  # detailed格式
                      if title:
                          return f"{title}\n{click_url}\n{tpwd}"
                      else:
                          return f"{click_url}\n{tpwd}"

  logger.error(f"淘口令转换失败: {tpwd}")
  return tpwd

async def create_taobao_tpwd(url, text=""):
  """
  生成淘口令
  """
  if not TAOBAO_API_CONFIG.get("enable_taobao_tpwd"):
      return url

  params = {
      "url": url,
      "text": text or "好物推荐"
  }

  response = await call_taobao_api("taobao.tbk.tpwd.create", params)
  if response and "tbk_tpwd_create_response" in response:
      data = response["tbk_tpwd_create_response"].get("data", {})
      model = data.get("model")
      if model:
          logger.info(f"淘口令生成成功: {url} -> {model}")
          return model

  logger.error(f"淘口令生成失败: {url}")
  return url

async def call_jd_api(material_id, is_union_link):
  """
  调用京东API生成短链接
  """
  if is_union_link:
      param_json = f'{{"promotionCodeReq":{{"materialId":"{material_id}","unionId":"{JD_API_CONFIG["union_id"]}","positionId":"{JD_API_CONFIG["position_id"]}"}}}}'
  else:
      param_json = f'{{"promotionCodeReq":{{"materialId":"{material_id}","unionId":"{JD_API_CONFIG["union_id2"]}"}}}}'

  encoded_param_json = quote(param_json)
  timestamp = str(int(time.time()))
  sign = generate_sign(JD_API_CONFIG["app_key"], JD_API_CONFIG["method"], param_json, timestamp,
                       JD_API_CONFIG["secret_key"])

  data = (
      f"app_key={JD_API_CONFIG['app_key']}&"
      f"format={JD_API_CONFIG['format_type']}&"
      f"method={JD_API_CONFIG['method']}&"
      f"param_json={encoded_param_json}&"
      f"sign={sign}&"
      f"sign_method={JD_API_CONFIG['sign_method']}&"
      f"timestamp={timestamp}&"
      f"v={JD_API_CONFIG['v']}"
  )

  logger.info(f"请求参数: {data}")

  try:
      async with session_no_ssl.post("https://router.jd.com/api",
                                     headers={"Content-Type": "application/x-www-form-urlencoded"},
                                     data=data) as response:
          response_text = await response.text()
          logger.info(f"京东 API 响应: {response_text}")

          response_data = json.loads(response_text)
          jd_response = response_data.get('jd_union_open_promotion_byunionid_get_response', {})
          result = json.loads(jd_response.get('result', '{}'))

          if 'code' in result and result['code'] != 200:
              error_message = result.get('message', '未知错误')
              logger.error(f"京东 API 返回错误: {error_message}")
              return f"[链接生成失败: {error_message}]"

          short_url = result.get('data', {}).get('shortURL')
          if short_url:
              return short_url
          else:
              logger.error("响应中没有 shortURL")
              return "[链接生成失败: 未找到短链接]"
  except Exception as e:
      logger.error(f"调用京东 API 时出错: {e}")
      return f"[链接生成失败: {str(e)}]"

async def process_prodev_link(url):
  """
  处理 Prodev 链接，提取优惠券信息
  """
  try:
      logger.info(f"访问 Prodev 链接: {url}")
      headers = {
          'User-Agent': (''),
          "Cookie": 'pt_key=AAJnWIHXADBZ--Gcq7c1wzi2aQytEc1nF1lXkAdm8pizFns5ovodw1-mIVDPZQO_UFeZ0edT760;pt_pin=jd_4b25e12eb2177;'
      }
      # 使用禁用 SSL 验证的会话对象
      async with session_no_ssl.get(url, headers=headers, timeout=10) as response:
          if response.status != 200:
              logger.error(f"获取 Prodev 链接失败，状态码: {response.status}")
              return f"[提取 API 失败: 无法访问链接] {url}"
          html_content = await response.text()
          logger.debug("成功获取 Prodev 页面内容")

      # 提取活动 ID
      activity_id_match = re.search(r'/active/(\w+)/index\.html', url)
      activity_id = activity_id_match.group(1) if activity_id_match else '未找到 activity_id'
      logger.debug(f"活动 ID: {activity_id}")

      # 解析 HTML 内容
      soup = BeautifulSoup(html_content, 'html.parser')

      # 提取所有 <script> 标签中的 JSON 数据
      scripts = soup.find_all('script')
      api_data = None

      for script in scripts:
          if script.string:
              # 尝试解析整个脚本内容为 JSON
              try:
                  json_data = json.loads(script.string)
                  # 递归查找 'couponList'
                  coupon_lists = find_key_recursive(json_data, 'couponList')
                  if coupon_lists:
                      api_data = {'couponList': coupon_lists}
                      logger.debug("成功找到 couponList 在 JSON 脚本中")
                      break
              except json.JSONDecodeError:
                  # 如果脚本内容不是纯 JSON，尝试使用正则表达式提取
                  match = re.search(r'window\.__api_data__\s*=\s*(\{.*?\});', script.string, re.DOTALL)
                  if match:
                      json_text = match.group(1)
                      try:
                          json_text = re.sub(r';$', '', json_text)  # 移除结尾的分号
                          json_data = json.loads(json_text)
                          # 递归查找 'couponList'
                          coupon_lists = find_key_recursive(json_data, 'couponList')
                          if coupon_lists:
                              api_data = {'couponList': coupon_lists}
                              logger.debug("成功找到 couponList 在 window.__api_data__ 中")
                              break
                      except json.JSONDecodeError as e:
                          logger.error(f"解析 JSON 出错: {e}")
                          continue

      if not api_data:
          logger.error("在所有 <script> 标签中找不到 couponList。")
          return f"[提取 API 失败: 页面结构异常] {url}"

      # 提取 couponList
      coupon_list = api_data.get('couponList', [])
      if not coupon_list:
          logger.error("在 api_data 中找不到 couponList。")
          return f"[提取 API 失败: 无法获取优惠券列表] {url}"

      # 如果 couponList 是一个嵌套的列表，展平成单一列表
      flattened_coupon_list = []
      for cl in coupon_list:
          if isinstance(cl, list):
              flattened_coupon_list.extend(cl)
          else:
              flattened_coupon_list.append(cl)

      logger.debug(f"Extracted coupon_list: {json.dumps(flattened_coupon_list, ensure_ascii=False, indent=2)}")

      coupon_infos = []
      for idx, coupon in enumerate(flattened_coupon_list, start=1):
          if not isinstance(coupon, dict):
              logger.error(f"优惠券数据格式不正确，索引 {idx}，数据：{coupon}")
              continue

          # 尝试从顶层提取字段
          args_value = coupon.get('args', '')
          end_period_value = coupon.get('endPeriod', 'N/A')
          limit_str = coupon.get('limit', 'N/A').replace('可用', '')
          discount_str = coupon.get('discount', 'N/A')
          scope = coupon.get('scope', 'N/A')

          # 如果顶层字段缺失，尝试从 flexibleData 中提取
          if end_period_value == 'N/A':
              end_period_value = coupon.get('flexibleData', {}).get('endPeriod', 'N/A')
          if limit_str == 'N/A':
              limit_str = coupon.get('flexibleData', {}).get('limit', 'N/A').replace('可用', '')
          if discount_str == 'N/A':
              discount_str = coupon.get('flexibleData', {}).get('discount', 'N/A')
          if scope == 'N/A':
              scope = coupon.get('flexibleData', {}).get('scope', 'N/A')

          coupon_name = f"{limit_str}-{discount_str}"
          body_dict = {
              "activityId": activity_id,
              "scene": "1",
              "args": args_value
          }
          body = quote(json.dumps(body_dict, separators=(',', ':')))
          api_url = f'https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={body}'

          logger.debug(f"构建 API URL: {api_url}")

          coupon_info = (
              f"#{idx}\n"
              f"优惠券名称: {coupon_name}\n"
              f"可用范围: {scope}\n"
              f"活动有效期: {end_period_value}\n"
              f"API 地址为:\n{api_url}\n"
          )
          coupon_infos.append(coupon_info)

      logger.info(f"成功提取 {len(coupon_infos)} 个优惠券信息")
      return "\n".join(coupon_infos)

  except asyncio.TimeoutError:
      logger.error(f"处理 Prodev 链接超时: {url}")
      return f"[提取 API 失败: 请求超时] {url}"
  except aiohttp.ClientError as e:
      logger.error(f"处理 Prodev 链接时发生网络错误: {str(e)}")
      return f"[提取 API 失败: 网络错误] {url}"
  except Exception as e:
      logger.error(f"处理 Prodev 链接时发生未知错误: {str(e)}")
      return f"[提取 API 失败: 未知错误] {url}"

async def process_kouling(kouling):
  """
  处理口令，调用口令API解析
  """
  data = {
      "url": kouling,
      "type": "kl",
      "u": "jApp",
      "model": "json"
  }
  try:
      async with session_default.post(KOULING_API_URL, headers=JD_KOULING_API["headers"], data=data) as response:
          content_type = response.headers.get('Content-Type', '').lower()
          if 'application/json' in content_type:
              response_json = await response.json()
          else:
              response_text = await response.text()
              response_json = json.loads(response_text)

          if response_json.get("status") == 200:
              code = response_json.get("code", "")
              title_match = re.search(r'【标题】\n(.*?)\n', code)
              link_match = re.search(r'【链接】\n(.*?)$', code, re.MULTILINE)

              if title_match and link_match:
                  return f"{title_match.group(1)}\n{link_match.group(1)}"
              else:
                  return kouling  # 如果无法提取标题和链接，返回原始口令
          else:
              logger.error(f"处理口令失败: {response_json.get('msg', '未知错误')}")
              return kouling
  except Exception as e:
      logger.error(f"处理口令时发生错误: {str(e)}")
      return kouling

def split_message(text, max_length=4096):
  """
  将长消息拆分为多个不超过 max_length 的部分
  """
  if len(text) <= max_length:
      return [text]

  lines = text.split('\n')
  messages = []
  current_message = ""

  for line in lines:
      # +1 是因为每行后面会加一个换行符
      if len(current_message) + len(line) + 1 > max_length:
          messages.append(current_message)
          current_message = line + '\n'
      else:
          current_message += line + '\n'

  if current_message:
      messages.append(current_message)

  return messages

async def short_to_real(short_url):
    """
    解析3.cn的短链接，获取真实的长链接

    Args:
        short_url (str): 短链接

    Returns:
        str: 真实的长链接或错误信息
    """
    logger.info(f"尝试解析短链接: {short_url}")
    try:
        # 使用禁用 SSL 验证的会话对象
        async with session_no_ssl.get(short_url, allow_redirects=True, timeout=10) as response:
            resolved_url = str(response.url)
            logger.info(f"解析成功: {short_url} -> {resolved_url}")
            return resolved_url
    except asyncio.TimeoutError:
        logger.error(f"解析短链接超时: {short_url}")
        return f"[解析失败: 请求超时]"
    except aiohttp.ClientError as e:
        logger.error(f"解析短链接时发生网络错误: {short_url}, 错误: {e}")
        return f"[解析失败: 网络错误]"
    except Exception as e:
        logger.error(f"解析短链接时发生未知错误: {short_url}, 错误: {e}")
        return f"[解析失败: 未知错误]"


async def process_message_content(message_text):
  """
  处理消息内容，解析链接并进行相应处理
  """
  logger.info(f"处理消息: {message_text}")

  message_lines = message_text.splitlines()
  new_message_lines = []

  for line in message_lines:
      # 处理京东链接
      jd_links = re.findall(JD_LINK_PATTERN, line)
      if jd_links:
          for link in jd_links:
              logger.info(f"提取到的京东链接: {link}")
              is_union_link = "u.jd.com" in link
              short_url = await call_jd_api(link, is_union_link)

              if short_url:
                  line = line.replace(link, short_url)
              else:
                  line = line.replace(link, f"[生成失败]({link})")

      # 处理Prodev链接
      prodev_links = re.findall(PRODEV_PATTERN, line)
      if prodev_links:
          for link in prodev_links:
              logger.info(f"提取到的Prodev链接: {link}")
              api_urls = await process_prodev_link(link)
              if api_urls:
                  new_message_lines.append(api_urls)
              else:
                  new_message_lines.append(f"[生成失败]({link})")
          continue  # 跳过添加此行，因为已添加处理后的Prodev链接

      # 处理口令
      kouling_matches = re.findall(KOULING_PATTERN, line)
      if kouling_matches:
          for kouling in kouling_matches:
              logger.info(f"提取到的口令: {kouling}")
              processed_result = await process_kouling(kouling)
              line = line.replace(kouling, processed_result)

      # 处理淘宝商品链接
      taobao_links = re.findall(TAOBAO_LINK_PATTERN, line)
      if taobao_links:
          for link in taobao_links:
              logger.info(f"提取到的淘宝链接: {link}")
              converted_url = await convert_taobao_link(link)
              if converted_url != link:
                  line = line.replace(link, converted_url)

      # 处理淘宝短链接
      taobao_short_links = re.findall(TAOBAO_SHORT_LINK_PATTERN, line)
      if taobao_short_links:
          for link in taobao_short_links:
              logger.info(f"提取到的淘宝短链接: {link}")
              # 先解析短链接获取真实链接
              resolved_url = await short_to_real(link)
              if resolved_url and resolved_url.startswith("http"):
                  # 再转换为推广链接
                  converted_url = await convert_taobao_link(resolved_url)
                  line = line.replace(link, converted_url)
              else:
                  line = line.replace(link, f"[解析失败]({link})")

      # 处理淘口令
      taobao_tpwds = re.findall(TAOBAO_TPWD_PATTERN, line)
      if taobao_tpwds:
          for tpwd in taobao_tpwds:
              logger.info(f"提取到的淘口令: {tpwd}")
              converted_result = await convert_taobao_tpwd(tpwd)
              if converted_result != tpwd:
                  line = line.replace(tpwd, converted_result)

      # 处理3.cn短链接
      short_urls = re.findall(SHORT_URL_PATTERN, line)
      if short_urls:
          for short_url in short_urls:
              logger.info(f"提取到的短链接: {short_url}")
              resolved_url = await short_to_real(short_url)
              if resolved_url and resolved_url.startswith("http"):
                  line = line.replace(short_url, resolved_url)
              else:
                  line = line.replace(short_url, f"[解析失败]({short_url})")

      new_message_lines.append(line)

  new_message = "\n".join(new_message_lines)
  return new_message if new_message != message_text else None

# astrbot插件类
@register("xianbao_processor", "作者", "线报链接处理插件", "1.0.0", "https://github.com/example/xianbao_processor")
class XianbaoProcessor(Star):
    def __init__(self, context: Context, config: AstrBotConfig = None):
        super().__init__(context)
        self.config = config or {}

        # 更新全局配置
        self.update_global_config()

        # 初始化会话
        asyncio.create_task(self.initialize_sessions())

    def update_global_config(self):
        """根据用户配置更新全局配置变量"""
        global JD_API_CONFIG, TAOBAO_API_CONFIG, KOULING_API_URL, ENABLE_PROCESSING

        if self.config:
            # 更新京东API配置
            if "jd_api" in self.config:
                jd_config = self.config["jd_api"]
                JD_API_CONFIG.update({
                    "app_key": jd_config.get("app_key", DEFAULT_JD_API_CONFIG["app_key"]),
                    "secret_key": jd_config.get("secret_key", DEFAULT_JD_API_CONFIG["secret_key"]),
                    "union_id": jd_config.get("union_id", DEFAULT_JD_API_CONFIG["union_id"]),
                    "union_id2": jd_config.get("union_id2", DEFAULT_JD_API_CONFIG["union_id2"]),
                    "position_id": jd_config.get("position_id", DEFAULT_JD_API_CONFIG["position_id"])
                })

            # 更新淘宝API配置
            if "taobao_api" in self.config:
                taobao_config = self.config["taobao_api"]
                TAOBAO_API_CONFIG.update({
                    "app_key": taobao_config.get("app_key", DEFAULT_TAOBAO_API_CONFIG["app_key"]),
                    "app_secret": taobao_config.get("app_secret", DEFAULT_TAOBAO_API_CONFIG["app_secret"]),
                    "adzone_id": taobao_config.get("adzone_id", DEFAULT_TAOBAO_API_CONFIG["adzone_id"]),
                    "sub_pid": taobao_config.get("sub_pid", DEFAULT_TAOBAO_API_CONFIG["sub_pid"]),
                    "enable_taobao_convert": taobao_config.get("enable_taobao_convert", DEFAULT_TAOBAO_API_CONFIG["enable_taobao_convert"]),
                    "enable_taobao_tpwd": taobao_config.get("enable_taobao_tpwd", DEFAULT_TAOBAO_API_CONFIG["enable_taobao_tpwd"]),
                    "taobao_format_style": taobao_config.get("taobao_format_style", DEFAULT_TAOBAO_API_CONFIG["taobao_format_style"])
                })

            # 更新口令API配置
            if "kouling_api" in self.config:
                KOULING_API_URL = self.config["kouling_api"].get("url", KOULING_API_URL)

            # 更新处理开关
            ENABLE_PROCESSING = self.config.get("enable_processing", True)

        logger.info(f"配置已更新: 处理功能{'启用' if ENABLE_PROCESSING else '禁用'}")

    async def initialize_sessions(self):
        """初始化全局 aiohttp 会话"""
        global session_default, session_no_ssl
        if session_default is None:
            session_default = aiohttp.ClientSession()
        if session_no_ssl is None:
            session_no_ssl = aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context_no_verify))
        logger.info("会话初始化完成")

    @filter.event_message_type(filter.EventMessageType.ALL)
    async def on_message(self, event: AstrMessageEvent):
        """处理所有消息，检测并处理线报链接"""
        # 检查是否启用处理功能
        if not ENABLE_PROCESSING:
            return

        message_text = event.message_str

        # 检查消息是否包含需要处理的链接
        if not (re.search(JD_LINK_PATTERN, message_text) or
                re.search(PRODEV_PATTERN, message_text) or
                re.search(KOULING_PATTERN, message_text) or
                re.search(SHORT_URL_PATTERN, message_text) or
                re.search(TAOBAO_LINK_PATTERN, message_text) or
                re.search(TAOBAO_SHORT_LINK_PATTERN, message_text) or
                re.search(TAOBAO_TPWD_PATTERN, message_text)):
            return  # 没有需要处理的链接，直接返回

        try:
            # 处理消息内容
            processed_message = await process_message_content(message_text)

            if processed_message:
                # 分割长消息并发送
                split_messages = split_message(processed_message)
                for msg in split_messages:
                    yield event.plain_result(msg)
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            yield event.plain_result(f"处理消息时发生错误: {str(e)}")

    @filter.command("xianbao")
    async def xianbao_command(self, event: AstrMessageEvent, action: str = "status"):
        """线报处理插件控制指令"""
        global ENABLE_PROCESSING

        if action == "status":
            status = "启用" if ENABLE_PROCESSING else "禁用"
            yield event.plain_result(f"线报处理功能当前状态: {status}")
        elif action == "enable":
            ENABLE_PROCESSING = True
            yield event.plain_result("线报处理功能已启用")
        elif action == "disable":
            ENABLE_PROCESSING = False
            yield event.plain_result("线报处理功能已禁用")
        elif action == "help":
            help_text = """线报处理插件使用说明:
/xianbao status - 查看当前状态
/xianbao enable - 启用处理功能
/xianbao disable - 禁用处理功能
/xianbao help - 显示帮助信息

支持的链接类型:
- 京东商品链接 (item.jd.com, u.jd.com等)
- Prodev活动链接 (prodev.m.jd.com, pro.m.jd.com)
- 京东口令 (格式: 数字:/￥...￥)
- 淘宝商品链接 (item.taobao.com, detail.tmall.com等)
- 淘宝短链接 (m.tb.cn, tb.cn)
- 淘口令 (格式: ￥...￥)
- 3.cn短链接

淘宝信息显示格式:
- detailed: 显示标题+链接+口令 (默认)
- simple: 只显示转换后的链接

注意: 淘宝转链功能需要在配置中启用并设置相关API参数"""
            yield event.plain_result(help_text)
        else:
            yield event.plain_result("未知操作，使用 /xianbao help 查看帮助")

    async def terminate(self):
        """插件卸载时关闭会话"""
        await self.close_sessions()

    async def close_sessions(self):
        """关闭全局 aiohttp 会话"""
        global session_default, session_no_ssl
        if session_default:
            await session_default.close()
            session_default = None
            logger.info("已关闭 session_default")
        if session_no_ssl:
            await session_no_ssl.close()
            session_no_ssl = None
            logger.info("已关闭 session_no_ssl")

# 插件入口点 - astrbot会自动加载插件类，不需要main函数