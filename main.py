#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AstrBot 京东线报处理插件 - 模块化版本
支持京东和淘宝联盟转链功能

作者: 插件开发者
版本: v1.1.0
"""

import aiohttp
import asyncio
import ssl
from astrbot.api.event import filter, AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api import logger, AstrBotConfig

# 导入自定义模块
from utils.config_manager import ConfigManager
from utils.message_processor import MessageProcessor

class XianbaoPlugin(Star):
    """线报处理插件主类"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.message_processor = None
        self.session_default = None
        self.session_no_ssl = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 创建HTTP会话
        self.session_default = aiohttp.ClientSession()
        
        # 创建禁用SSL验证的会话
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        self.session_no_ssl = aiohttp.ClientSession(connector=connector)
        
        # 初始化消息处理器
        self.message_processor = MessageProcessor(
            self.config_manager,
            self.session_default,
            self.session_no_ssl
        )
        
        logger.info("线报处理插件已启动")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session_default:
            await self.session_default.close()
        if self.session_no_ssl:
            await self.session_no_ssl.close()
        logger.info("线报处理插件已关闭")
    
    def update_global_config(self):
        """根据用户配置更新全局配置变量"""
        self.config_manager.update_config(self.config)
        
        # 重新初始化消息处理器以应用新配置
        if self.message_processor:
            self.message_processor = MessageProcessor(
                self.config_manager,
                self.session_default,
                self.session_no_ssl
            )
    
    @filter.command("xianbao")
    async def handle_xianbao_command(self, ctx: Context):
        """处理xianbao命令"""
        message = ctx.message_str
        parts = message.split()
        
        if len(parts) < 2:
            await ctx.send_message(self.message_processor.get_help_text())
            return
        
        command = parts[1].lower()
        
        if command == "status":
            status_text = self.message_processor.get_status_text()
            await ctx.send_message(status_text)
        
        elif command == "enable":
            self.config_manager.enable_processing = True
            await ctx.send_message("✅ 线报处理功能已启用")
        
        elif command == "disable":
            self.config_manager.enable_processing = False
            await ctx.send_message("❌ 线报处理功能已禁用")
        
        elif command == "help":
            help_text = self.message_processor.get_help_text()
            await ctx.send_message(help_text)
        
        else:
            await ctx.send_message("未知命令，请使用 /xianbao help 查看帮助")
    
    @filter.message_type("text")
    async def handle_text_message(self, ctx: Context):
        """处理文本消息"""
        if not self.config_manager.is_processing_enabled():
            return
        
        message_text = ctx.message_str
        
        # 跳过命令消息
        if message_text.startswith('/'):
            return
        
        try:
            # 处理消息中的链接
            processed_text = await self.message_processor.process_message_links(message_text)
            
            # 如果消息有变化，发送处理后的消息
            if processed_text != message_text:
                # 分割长消息
                message_chunks = self.message_processor.split_message(processed_text)
                
                for chunk in message_chunks:
                    await ctx.send_message(chunk)
                    if len(message_chunks) > 1:
                        await asyncio.sleep(0.5)  # 避免发送过快
        
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            await ctx.send_message(f"处理消息时发生错误: {str(e)}")

# 插件注册
@register(
    name="线报处理插件",
    description="自动处理京东和淘宝线报链接，支持转链和信息提取",
    version="1.1.0",
    author="插件开发者"
)
class XianbaoPluginWrapper:
    """插件包装器"""
    
    def __init__(self, config: AstrBotConfig):
        self.config = config
        self.plugin_instance = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.plugin_instance = XianbaoPlugin()
        self.plugin_instance.config = self.config.get("xianbao", {})
        self.plugin_instance.update_global_config()
        
        await self.plugin_instance.__aenter__()
        return self.plugin_instance
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.plugin_instance:
            await self.plugin_instance.__aexit__(exc_type, exc_val, exc_tb)

# 全局插件实例
plugin_instance = None

async def initialize_plugin(config: AstrBotConfig):
    """初始化插件"""
    global plugin_instance
    if plugin_instance is None:
        wrapper = XianbaoPluginWrapper(config)
        plugin_instance = await wrapper.__aenter__()
    return plugin_instance

async def cleanup_plugin():
    """清理插件"""
    global plugin_instance
    if plugin_instance:
        await plugin_instance.__aexit__(None, None, None)
        plugin_instance = None

# 事件处理器
@filter.command("xianbao")
async def xianbao_command_handler(ctx: Context):
    """xianbao命令处理器"""
    plugin = await initialize_plugin(ctx.config)
    await plugin.handle_xianbao_command(ctx)

@filter.message_type("text")
async def text_message_handler(ctx: Context):
    """文本消息处理器"""
    plugin = await initialize_plugin(ctx.config)
    await plugin.handle_text_message(ctx)
