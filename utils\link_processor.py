#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
链接处理工具模块
包含各种链接的识别、解析和处理功能
"""

import re
import json
import aiohttp
from bs4 import BeautifulSoup
from astrbot.api import logger

# 正则表达式模式
JD_LINK_PATTERN = r'(https://(?:u|item|item\.m|jingfen)\.jd\.com/\S*)'
PRODEV_PATTERN = r'(https://(?:prodev\.m\.jd\.com|pro\.m\.jd\.com)/\S*)'
KOULING_PATTERN = r'(\d+:/[^\n￥]*￥[A-Za-z0-9]+￥)'
SHORT_URL_PATTERN = r'(https?://3\.cn/\S+)'

# 淘宝链接正则表达式
TAOBAO_LINK_PATTERN = r'(https://(?:item\.taobao\.com|detail\.tmall\.com|chaoshi\.detail\.tmall\.com|detail\.tmall\.hk)/[^\s]*)'
TAOBAO_SHORT_LINK_PATTERN = r'(https://(?:m\.tb\.cn|tb\.cn)/[^\s]*)'
TAOBAO_TPWD_PATTERN = r'(￥[A-Za-z0-9]+￥)'

class LinkProcessor:
    """链接处理器类"""
    
    def __init__(self, kouling_api_url):
        self.kouling_api_url = kouling_api_url
        self.kouling_headers = {
            "Host": "jd.zack.xin",
            "Connection": "keep-alive",
            "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua-platform": '"Windows"',
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://jd.zack.xin",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://jd.zack.xin/jd/zhushou/",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
        }
    
    def extract_jd_material_id(self, url):
        """
        从京东链接中提取material_id
        """
        # 从不同格式的京东链接中提取ID
        patterns = [
            r'/(\d+)\.html',  # item.jd.com/12345.html
            r'sku=(\d+)',     # 参数中的sku
            r'skuId=(\d+)',   # 参数中的skuId
            r'/item/(\d+)',   # 其他格式
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        logger.warning(f"无法从京东链接中提取商品ID: {url}")
        return None
    
    def is_union_link(self, url):
        """
        判断是否为京东联盟链接
        """
        union_domains = ['u.jd.com', 'jingfen.jd.com']
        return any(domain in url for domain in union_domains)
    
    async def short_to_real(self, short_url, session):
        """
        解析短链接获取真实地址
        """
        try:
            async with session.get(short_url, allow_redirects=False, timeout=10) as response:
                if response.status in [301, 302, 303, 307, 308]:
                    real_url = response.headers.get('Location')
                    if real_url:
                        logger.info(f"短链接解析成功: {short_url} -> {real_url}")
                        return real_url
                    else:
                        logger.error(f"短链接解析失败，未找到重定向地址: {short_url}")
                        return short_url
                else:
                    logger.error(f"短链接解析失败，状态码: {response.status}")
                    return short_url
        except Exception as e:
            logger.error(f"解析短链接时出错: {e}")
            return short_url
    
    async def process_kouling(self, kouling, session):
        """
        处理京东口令，调用口令API解析
        """
        data = {
            "url": kouling,
            "type": "kl",
            "u": "jApp",
            "model": "json"
        }
        
        try:
            async with session.post(self.kouling_api_url, headers=self.kouling_headers, data=data) as response:
                content_type = response.headers.get('Content-Type', '').lower()
                if 'application/json' in content_type:
                    response_json = await response.json()
                    if response_json.get('code') == 200:
                        data_content = response_json.get('data', '')
                        # 提取标题和链接
                        title_match = re.search(r'【([^】]+)】', data_content)
                        link_match = re.search(r'(https://[^\s]+)', data_content)
                        
                        if title_match and link_match:
                            return f"{title_match.group(1)}\n{link_match.group(1)}"
                        else:
                            return kouling
                    else:
                        logger.error(f"处理口令失败: {response_json.get('msg', '未知错误')}")
                        return kouling
        except Exception as e:
            logger.error(f"处理口令时发生错误: {str(e)}")
            return kouling
    
    def find_key_recursive(self, data, target_key):
        """
        递归查找字典中的键值
        """
        results = []
        if isinstance(data, dict):
            for key, value in data.items():
                if key == target_key:
                    results.append(value)
                elif isinstance(value, (dict, list)):
                    results.extend(self.find_key_recursive(value, target_key))
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    results.extend(self.find_key_recursive(item, target_key))
        return results
    
    def has_links_to_process(self, text):
        """
        检查文本是否包含需要处理的链接
        """
        patterns = [
            JD_LINK_PATTERN,
            PRODEV_PATTERN, 
            KOULING_PATTERN,
            SHORT_URL_PATTERN,
            TAOBAO_LINK_PATTERN,
            TAOBAO_SHORT_LINK_PATTERN,
            TAOBAO_TPWD_PATTERN
        ]
        
        return any(re.search(pattern, text) for pattern in patterns)
    
    def extract_links_by_type(self, text):
        """
        按类型提取文本中的链接
        """
        return {
            'jd_links': re.findall(JD_LINK_PATTERN, text),
            'prodev_links': re.findall(PRODEV_PATTERN, text),
            'kouling': re.findall(KOULING_PATTERN, text),
            'short_urls': re.findall(SHORT_URL_PATTERN, text),
            'taobao_links': re.findall(TAOBAO_LINK_PATTERN, text),
            'taobao_short_links': re.findall(TAOBAO_SHORT_LINK_PATTERN, text),
            'taobao_tpwd': re.findall(TAOBAO_TPWD_PATTERN, text)
        }

    async def process_prodev_link(self, url, session_no_ssl):
        """
        处理 Prodev 链接，提取优惠券信息
        """
        try:
            logger.info(f"访问 Prodev 链接: {url}")
            headers = {
                'User-Agent': (''),
                "Cookie": 'pt_key=AAJnWIHXADBZ--Gcq7c1wzi2aQytEc1nF1lXkAdm8pizFns5ovodw1-mIVDPZQO_UFeZ0edT760;pt_pin=jd_4b25e12eb2177;'
            }

            # 使用禁用 SSL 验证的会话对象
            async with session_no_ssl.get(url, headers=headers, timeout=10) as response:
                if response.status != 200:
                    logger.error(f"获取 Prodev 链接失败，状态码: {response.status}")
                    return f"[提取 API 失败: 无法访问链接] {url}"
                html_content = await response.text()
                logger.debug("成功获取 Prodev 页面内容")

            # 提取活动 ID
            activity_id_match = re.search(r'/active/(\w+)/index\.html', url)
            if not activity_id_match:
                logger.error("无法从 URL 中提取活动 ID")
                return f"[提取 API 失败: 无法解析活动ID] {url}"

            activity_id = activity_id_match.group(1)
            logger.info(f"提取到活动 ID: {activity_id}")

            # 解析 HTML 内容
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有脚本标签
            scripts = soup.find_all('script')
            coupon_infos = []

            for script in scripts:
                if script.string:
                    script_content = script.string

                    # 查找优惠券相关的 JSON 数据
                    json_matches = re.findall(r'\{[^{}]*"couponInfo"[^{}]*\}', script_content)

                    for json_match in json_matches:
                        try:
                            # 尝试解析 JSON
                            coupon_data = json.loads(json_match)
                            coupon_info = coupon_data.get('couponInfo', {})

                            if coupon_info:
                                # 提取优惠券信息
                                discount = coupon_info.get('discount', '')
                                quota = coupon_info.get('quota', '')

                                if discount and quota:
                                    coupon_text = f"优惠券: 满{quota}减{discount}"
                                    coupon_infos.append(coupon_text)
                                    logger.info(f"提取到优惠券信息: {coupon_text}")

                        except json.JSONDecodeError:
                            continue

                    # 查找 API 相关信息
                    api_matches = re.findall(r'"apiUrl":\s*"([^"]+)"', script_content)
                    for api_url in api_matches:
                        if 'api.m.jd.com' in api_url:
                            coupon_infos.append(f"API地址: {api_url}")
                            logger.info(f"提取到 API 地址: {api_url}")

            if coupon_infos:
                logger.info(f"成功提取 Prodev 信息，共 {len(coupon_infos)} 条")
                return "\n".join(coupon_infos)
            else:
                logger.warning("未找到优惠券信息")
                return f"[未找到优惠券信息] {url}"

        except Exception as e:
            logger.error(f"处理 Prodev 链接时发生错误: {str(e)}")
            return f"[提取 API 失败: 未知错误] {url}"
