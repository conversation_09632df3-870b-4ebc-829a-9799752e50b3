#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
处理插件的配置加载、验证和更新
"""

from astrbot.api import logger

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        # 默认京东API配置
        self.default_jd_config = {
            "app_key": "4363c9848d8a43c8b7b9e957d3adcbcf",
            "method": "jd.union.open.promotion.byunionid.get",
            "format_type": "json",
            "sign_method": "md5",
            "v": "1.0",
            "secret_key": "905878013e044d2ba3adeebb05338086",
            "union_id": "1000054340",
            "union_id2": "1002084276",
            "position_id": "3003477075"
        }
        
        # 默认淘宝API配置
        self.default_taobao_config = {
            "app_key": "",
            "app_secret": "",
            "adzone_id": "",
            "sub_pid": "",
            "enable_taobao_convert": False,
            "enable_taobao_tpwd": False
        }
        
        # 当前配置
        self.jd_config = self.default_jd_config.copy()
        self.taobao_config = self.default_taobao_config.copy()
        self.kouling_api_url = "https://jd.zack.xin/api/jd/ulink.php"
        self.enable_processing = True
    
    def update_config(self, user_config):
        """
        根据用户配置更新全局配置变量
        """
        if not user_config:
            logger.info("使用默认配置")
            return
        
        # 更新京东API配置
        if "jd_api" in user_config:
            jd_config = user_config["jd_api"]
            self.jd_config.update({
                "app_key": jd_config.get("app_key", self.default_jd_config["app_key"]),
                "secret_key": jd_config.get("secret_key", self.default_jd_config["secret_key"]),
                "union_id": jd_config.get("union_id", self.default_jd_config["union_id"]),
                "union_id2": jd_config.get("union_id2", self.default_jd_config["union_id2"]),
                "position_id": jd_config.get("position_id", self.default_jd_config["position_id"])
            })
            logger.info("京东API配置已更新")

        # 更新淘宝API配置
        if "taobao_api" in user_config:
            taobao_config = user_config["taobao_api"]
            self.taobao_config.update({
                "app_key": taobao_config.get("app_key", self.default_taobao_config["app_key"]),
                "app_secret": taobao_config.get("app_secret", self.default_taobao_config["app_secret"]),
                "adzone_id": taobao_config.get("adzone_id", self.default_taobao_config["adzone_id"]),
                "sub_pid": taobao_config.get("sub_pid", self.default_taobao_config["sub_pid"]),
                "enable_taobao_convert": taobao_config.get("enable_taobao_convert", self.default_taobao_config["enable_taobao_convert"]),
                "enable_taobao_tpwd": taobao_config.get("enable_taobao_tpwd", self.default_taobao_config["enable_taobao_tpwd"])
            })
            logger.info("淘宝API配置已更新")

        # 更新口令API配置
        if "kouling_api" in user_config:
            self.kouling_api_url = user_config["kouling_api"].get("url", self.kouling_api_url)
            logger.info("口令API配置已更新")

        # 更新处理开关
        self.enable_processing = user_config.get("enable_processing", True)
        
        logger.info(f"配置更新完成: 处理功能{'启用' if self.enable_processing else '禁用'}")
    
    def validate_jd_config(self):
        """
        验证京东API配置
        """
        required_fields = ["app_key", "secret_key", "union_id", "position_id"]
        missing_fields = []
        
        for field in required_fields:
            if not self.jd_config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            logger.warning(f"京东API配置不完整，缺少字段: {missing_fields}")
            return False
        
        return True
    
    def validate_taobao_config(self):
        """
        验证淘宝API配置
        """
        if not self.taobao_config.get("enable_taobao_convert") and not self.taobao_config.get("enable_taobao_tpwd"):
            return True  # 如果都未启用，则不需要验证
        
        required_fields = ["app_key", "app_secret", "adzone_id"]
        missing_fields = []
        
        for field in required_fields:
            if not self.taobao_config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            logger.warning(f"淘宝API配置不完整，缺少字段: {missing_fields}")
            return False
        
        return True
    
    def get_jd_config(self):
        """获取京东API配置"""
        return self.jd_config.copy()
    
    def get_taobao_config(self):
        """获取淘宝API配置"""
        return self.taobao_config.copy()
    
    def get_kouling_api_url(self):
        """获取口令API URL"""
        return self.kouling_api_url
    
    def is_processing_enabled(self):
        """检查处理功能是否启用"""
        return self.enable_processing
    
    def is_taobao_convert_enabled(self):
        """检查淘宝转链是否启用"""
        return self.taobao_config.get("enable_taobao_convert", False)
    
    def is_taobao_tpwd_enabled(self):
        """检查淘口令处理是否启用"""
        return self.taobao_config.get("enable_taobao_tpwd", False)
    
    def get_config_status(self):
        """
        获取配置状态信息
        """
        status = {
            "processing_enabled": self.enable_processing,
            "jd_config_valid": self.validate_jd_config(),
            "taobao_convert_enabled": self.is_taobao_convert_enabled(),
            "taobao_tpwd_enabled": self.is_taobao_tpwd_enabled(),
            "taobao_config_valid": self.validate_taobao_config()
        }
        
        return status
