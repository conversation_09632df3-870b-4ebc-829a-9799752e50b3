# 部署指南

## 📦 代码分类完成

您的淘宝联盟转链功能已经完成代码分类和模块化重构！现在有两个版本可供选择：

### 版本选择

#### 1. 原始版本 (main.py)
- **特点**: 单文件包含所有功能
- **优点**: 部署简单，只需一个文件
- **适用**: 快速部署，简单使用

#### 2. 模块化版本 (main_modular.py + utils/)
- **特点**: 代码分模块组织
- **优点**: 结构清晰，易于维护和扩展
- **适用**: 开发环境，需要定制功能

## 📁 文件结构

```
astrbot_jd/
├── main.py                     # 原始单文件版本 ⭐
├── main_modular.py            # 模块化版本主文件 ⭐
├── _conf_schema.json          # 配置模板 ⭐
├── README.md                  # 项目说明
├── CODE_STRUCTURE.md          # 代码结构说明
├── DEPLOYMENT_GUIDE.md        # 部署指南（本文件）
├── utils/                     # 工具模块包 ⭐
│   ├── __init__.py           # 包初始化
│   ├── config_manager.py     # 配置管理
│   ├── jd_api.py            # 京东API
│   ├── taobao_api.py        # 淘宝API
│   ├── link_processor.py    # 链接处理
│   └── message_processor.py # 消息处理
├── tests/                    # 测试文件
│   ├── test_taobao_convert.py
│   ├── check_taobao_config.py
│   └── test_imports.py
└── docs/                     # 文档
    └── taobao_config_example.md
```

⭐ 标记的文件是核心文件

## 🚀 部署步骤

### 方案一：使用原始版本（推荐新手）

1. **复制文件**
   ```bash
   # 只需要这两个文件
   cp main.py /path/to/astrbot/plugins/
   cp _conf_schema.json /path/to/astrbot/plugins/
   ```

2. **配置插件**
   - 在AstrBot管理面板中配置API密钥
   - 启用相应功能开关

3. **重启AstrBot**

### 方案二：使用模块化版本（推荐开发者）

1. **复制文件**
   ```bash
   # 复制所有核心文件
   cp main_modular.py /path/to/astrbot/plugins/
   cp _conf_schema.json /path/to/astrbot/plugins/
   cp -r utils/ /path/to/astrbot/plugins/
   ```

2. **重命名主文件**
   ```bash
   # 将模块化版本重命名为main.py
   mv main_modular.py main.py
   ```

3. **配置和重启**
   - 同方案一

## ⚙️ 配置说明

### 京东联盟配置
```json
{
  "jd_api": {
    "app_key": "你的京东APP_KEY",
    "secret_key": "你的京东SECRET_KEY",
    "union_id": "你的联盟ID",
    "union_id2": "备用联盟ID",
    "position_id": "推广位ID"
  }
}
```

### 淘宝联盟配置
```json
{
  "taobao_api": {
    "app_key": "你的淘宝APP_KEY",
    "app_secret": "你的淘宝APP_SECRET",
    "adzone_id": "你的推广位ID",
    "sub_pid": "三方分成PID（可选）",
    "enable_taobao_convert": true,
    "enable_taobao_tpwd": true
  }
}
```

### 功能开关
```json
{
  "enable_processing": true
}
```

## 🔧 功能特性

### 支持的链接类型
- ✅ 京东商品链接 (item.jd.com, u.jd.com等)
- ✅ Prodev活动链接 (prodev.m.jd.com)
- ✅ 京东口令 (数字:/￥...￥)
- ✅ 淘宝商品链接 (item.taobao.com, detail.tmall.com等)
- ✅ 淘宝短链接 (m.tb.cn, tb.cn)
- ✅ 淘口令 (￥...￥)
- ✅ 3.cn短链接

### 转链功能
- ✅ **口令转口令** - 淘口令解析并转换为推广链接
- ✅ **链接转链接** - 商品链接转换为推广链接
- ✅ **短链接解析** - 自动解析各种短链接
- ✅ **批量处理** - 单条消息中多个链接同时处理
- ✅ **错误处理** - 转换失败时返回原链接

## 🧪 测试验证

### 1. 基础测试
```bash
python test_imports.py
```

### 2. 功能测试
```bash
python test_taobao_convert.py
```

### 3. 配置检查
```bash
python check_taobao_config.py
```

## 📝 使用示例

### 命令使用
```
/xianbao status   # 查看状态
/xianbao enable   # 启用功能
/xianbao disable  # 禁用功能
/xianbao help     # 显示帮助
```

### 链接处理示例
```
输入: https://item.taobao.com/item.htm?id=123456789
输出: https://s.click.taobao.com/t?e=xxx（推广链接）

输入: ￥ABC123￥
输出: https://s.click.taobao.com/t?e=xxx（推广链接）

输入: https://item.jd.com/123456.html
输出: https://u.jd.com/xxx（推广短链）
```

## 🔍 故障排除

### 常见问题

1. **转链失败**
   - 检查API密钥是否正确
   - 确认推广位ID有效
   - 查看日志错误信息

2. **模块导入错误**
   - 确保文件结构正确
   - 检查utils包是否完整

3. **配置不生效**
   - 重启AstrBot
   - 检查配置文件格式

### 日志查看
在AstrBot日志中搜索关键词：
- "淘宝链接转换成功"
- "京东链接转换成功"
- "API调用失败"

## 📈 性能优化

### 建议设置
- 合理设置API调用频率
- 避免同时处理过多链接
- 定期检查API配额使用情况

### 监控指标
- API调用成功率
- 链接转换成功率
- 响应时间

## 🔄 版本升级

### 从v1.0升级到v1.1
1. 备份现有配置
2. 替换插件文件
3. 添加淘宝API配置
4. 重启AstrBot

## 📞 技术支持

### 获取帮助
- 查看README.md了解基本使用
- 查看CODE_STRUCTURE.md了解代码结构
- 查看taobao_config_example.md了解淘宝配置

### 问题反馈
如遇到问题，请提供：
- 错误日志
- 配置信息（隐藏敏感数据）
- 复现步骤

---

## 🎉 总结

您现在拥有了一个完整的、模块化的淘宝联盟转链插件！

### ✅ 已完成功能
- 京东联盟转链
- 淘宝联盟转链
- 口令转口令
- 链接转链接
- 代码模块化分类
- 完整的测试和文档

### 🚀 可扩展功能
- 添加更多电商平台支持
- 增加数据统计功能
- 添加缓存机制
- 实现批量API调用

选择适合您需求的版本开始使用吧！
